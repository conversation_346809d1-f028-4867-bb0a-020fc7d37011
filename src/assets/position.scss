/**位置固定**/
.abs-bottom {position:absolute;left:0;right:0;bottom:0;}
.abs-top {position:absolute;left:0;right:0;top:0;}
.top-sticky,
.bottom-sticky{position:-webkit-sticky!important;position:-moz-sticky!important;position:-ms-sticky!important;position:sticky!important;left:0;right:0;z-index:1;width:100%}
.top-sticky{top:-1px}
.bottom-sticky{bottom:0}
.top-fixed,.bottom-fixed{position:fixed;left:0;right:0}
.top-fixed{top:0;bottom:initial}
.bottom-fixed{top:initial;bottom:0}
.flex-center{display:flex;justify-content:center;align-items:center}
.horizontal, .vertical, .center, .left-center, .right-center,.vertical-center{display: flex!important;width: 100%;}
.horizontal{justify-content: start; align-items: start;}
.vertical{flex-direction: column;}
.center, .vertical-center{text-align: center!important; justify-content: center!important; align-items: center!important; vertical-align: middle!important;}
.left-center{text-align:left!important; align-items: center!important;}
.right-center{text-align:right!important; align-items: center!important;}
.vertical-center{flex-direction: column}
.align-baseline{align-items: baseline!important;}
.float-right {float: right;}
.float-left {float: left;}
.text-center {text-align: center;}
.text-left {text-align: left;}
.text-right {text-align: right;}
.flex-1{flex:1;}
.flex-2{flex:2;}
select.absolute{position:absolute;left:0;right:0;top:0;bottom:0;width:100%;height:100%;opacity:0}
.relative{position:relative}

.row {
  flex: 1;
  display: flex;
  flex-direction: row;
}

.col {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.row > .item, .col > .item {
  flex: 1;
}