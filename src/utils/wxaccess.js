export default [{
  id: 1,
  name: '消息管理权限',
  desc: '帮助公众号接收用户消息，进行人工客服回复或自动回复'
}, {
  id: 2,
  name: '用户管理权限',
  desc: '帮助公众号获取用户信息，进行用户管理'
}, {
  id: 3,
  name: '帐号服务权限',
  desc: '帮助认证、设置公众号，进行帐号管理'
}, {
  id: 4,
  name: '网页服务权限',
  desc: '帮助公众号实现第三方网页服务和活动'
}, {
  id: 5,
  name: '微信小店权限',
  desc: '帮助公众号使用微信小店'
}, {
  id: 6,
  name: '微信多客服权限',
  desc: '帮助公众号使用微信多客服'
}, {
  id: 7,
  name: '群发与通知权限',
  desc: '帮助公众号进行群发和模板消息业务通知'
}, {
  id: 8,
  name: '微信卡券权限',
  desc: '帮助公众号使用微信卡券'
}, {
  id: 9,
  name: '微信扫一扫权限',
  desc: '帮助公众号使用微信扫一扫'
}, {
  id: 10,
  name: '微信连WIFI权限',
  desc: '帮助公众号使用微信连WIFI'
}, {
  id: 11,
  name: '素材管理权限',
  desc: '帮助公众号管理多媒体素材，用于客服等业务'
}, {
  id: 12,
  name: '微信摇周边权限',
  desc: '帮助公众号使用微信摇周边'
}, {
  id: 13,
  name: '微信门店权限',
  desc: '帮助公众号使用微信门店'
}, {
  id: 15,
  name: '自定义菜单权限',
  desc: '帮助公众号使用自定义菜单'
}, {
  id: 22,
  name: '城市服务接口权限',
  desc: '帮助城市服务内的服务向用户发送消息，沉淀办事记录，展示页卡及办事结果页'
}, {
  id: 23,
  name: '广告管理权限',
  desc: '帮助广告主进行微信广告的投放和管理'
}, {
  id: 24,
  name: '开放平台帐号管理权限',
  desc: '帮助公众号绑定开放平台帐号，实现用户身份打通'
}, {
  id: 26,
  name: '微信电子发票权限',
  desc: '帮助公众号使用微信电子发票'
}, {
  id: 27,
  name: '快速注册小程序权限',
  desc: '帮助公众号快速注册小程序'
}, {
  id: 33,
  name: '小程序管理权限',
  desc: '可新增关联小程序，并对公众号已关联的小程序进行管理'
}, {
  id: 34,
  name: '微信商品库权限',
  desc: '帮助公众号商家导入、更新、查询商品信息，从而在返佣商品推广等场景使用'
}, {
  id: 35,
  name: '微信卡路里权限',
  desc: '为公众号提供用户卡路里同步、授权查询、兑换功能'
}, {
  id: 44,
  name: '好物圈权限',
  desc: '帮助公众号将物品、订单、收藏等信息同步至好物圈，方便用户进行推荐'
}, {
  id: 46,
  name: '微信一物一码权限',
  desc: '帮助公众号使用微信一物一码功能'
}, {
  id: 47,
  name: '微信财政电子票据权限',
  desc: '帮助公众号完成授权、插卡及报销'
}, {
  id: 54,
  name: '服务号对话权限',
  desc: '帮助公众号配置对话能力，管理顾问、客户、标签和素材等'
}, {
  id: 66,
  name: '服务平台管理权限',
  desc: '帮助公众号管理服务平台上购买的资源'
}, {
  id: 17,
  name: '帐号管理权限',
  desc: '帮助小程序获取二维码，进行帐号管理'
}, {
  id: 18,
  name: '开发管理与数据分析权限',
  desc: '帮助小程序进行功能开发与数据分析'
}, {
  id: 19,
  name: '客服消息管理权限',
  desc: '帮助小程序接收和发送客服消息'
}, {
  id: 25,
  name: '开放平台帐号管理权限',
  desc: '帮助小程序绑定开放平台帐号，实现用户身份打通'
}, {
  id: 30,
  name: '小程序基本信息设置权限',
  desc: '帮助小程序设置名称、头像、简介、类目等基本信息'
}, {
  id: 31,
  name: '小程序认证权限',
  desc: '帮助小程序申请认证'
}, {
  id: 36,
  name: '微信卡路里权限',
  desc: '为小程序提供用户卡路里同步、授权查询、兑换功能'
}, {
  id: 37,
  name: '附近地点权限',
  desc: '帮助小程序创建附近地点，可设置小程序展示在“附近的小程序”入口中'
}, {
  id: 40,
  name: '插件管理权限',
  desc: '用于代小程序管理插件的添加和使用'
}, {
  id: 41,
  name: '好物圈权限',
  desc: '帮助小程序将物品、订单、收藏等信息同步至好物圈，方便用户进行推荐'
}, {
  id: 45,
  name: '快递配送权限',
  desc: '帮助有快递配送需求的开发者，快速高效对接多家快递公司。对接后用户可通过微信服务通知接收实时快递配送状态，提升用户体验'
}, {
  id: 48,
  name: '微信财政电子票据权限',
  desc: '帮助小程序完成授权、插卡及报销'
}, {
  id: 49,
  name: '云开发管理权限',
  desc: '帮助小程序管理小程序·云开发资源'
}, {
  id: 51,
  name: '即时配送权限',
  desc: '旨在解决餐饮、生鲜、超市等小程序的外卖配送需求，接入后小程序商家可通过统一的接口获得多家配送公司的配送服务，提高经营效率'
}, {
  id: 52,
  name: '小程序直播权限',
  desc: '帮助有直播需求的小程序实现在小程序上直播边看边买的能力'
}, {
  id: 57,
  name: '页面推送权限',
  desc: '帮助小程序推送小程序页面给搜索引擎，增加小程序页面在搜索的收录与曝光机会'
}, {
  id: 65,
  name: '广告管理权限',
  desc: '帮助广告主进行微信广告的投放和管理'
}, {
  id: 67,
  name: '服务平台管理权限',
  desc: '帮助小程序管理服务平台上购买的资源'
}, {
  id: 70,
  name: '商品管理权限',
  desc: '支持对小商店商品及库存信息进行管理'
}, {
  id: 71,
  name: '订单与物流管理权限',
  desc: '支持对小商店订单及物流信息进行管理'
}]