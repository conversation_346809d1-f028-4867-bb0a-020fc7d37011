<template>
  <span class="login-agree">
    <el-checkbox v-model="value" @change="onChange">已阅读并同意</el-checkbox>
    <a href="javascript:">《用户使用协议》</a>
  </span>
</template>

<script>
const KEY = 'LoginAgree';

export default {
  props: ['value'],
  created() {
    this.onChange(localStorage.getItem(KEY));
  },
  methods: {
    onChange(v) {
      if (!v) localStorage.removeItem(KEY);
      else localStorage.setItem(KEY, '1');
      this.$emit('input', !!v);
    }
  }
}
</script>