<template>
  <div class="register-page">
    <component
      v-bind:is="view"
      @change="setType"
      @submit="submit"
      :loading="loading"
    ></component>
  </div>
</template>

<style lang="scss" src="./style.scss"></style>

<script>
import LoginByPassword from "./components/password";

export default {
  components: { LoginByPassword },
  data() {
    return {
      view: "login-by-password",
      loading: 0,
    };
  },
  methods: {
    setType(v, agree) {
      if (v == "weixin" && !agree) {
        return this.$message({
          message: "请阅读并同意用户使用协议",
          type: "warning",
        });
      }

      this.view = "login-by-" + v;
    },
    submit(model) {
      this.loading = 1;

      // let query = this.$route.query;npm 

      this.$store
        .dispatch("admin/register", model)
        .then((res) => {
          this.$message({
            message:"注册成功，等待审核",
            type:"success"
          })
          setTimeout(() => {
            location.replace("/login");
          }, 2000);
        })
        .finally(() => {
          this.loading = 0;
        });
    },
  },
};
</script>
