<template>
  <el-dialog class="contact-edit-dialog" :visible.sync="visible" title="回访备注" width="600px" @closed="$emit('closed')">
    <el-form label-width="90px" label-suffix="：">
      <el-row>
        <el-col :span="12">
          <el-form-item label="联系人">{{ info.name }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="提交时间">{{ create }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="电话号码">{{ info.telphone }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="微信号码">{{ info.telphone }}</el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="企业名称">{{ info.enterprise }}</el-form-item>
      <el-form-item label="合作内容">{{ info.business }}</el-form-item>
      <el-form-item label="回访状态">
        <el-radio-group v-model="info.state">
          <el-radio :label="1">回访中</el-radio>
          <el-radio :label="2">有意向</el-radio>
          <el-radio :label="3">无意向</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="回访备注">
        <el-input v-model="info.remark" type="textarea" :rows="4"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="submit">确 定</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  props: ['model'],
  data() {
    return {
      visible: true,
      info: Object.assign({}, this.model)
    }
  },
  computed: {
    create() {
      return this.$utils.Date(this.info.created).format()
    }
  },
  created() {
    this.$api.about.contact.get(this.model.id).then(data => {
      Object.assign(this.info, data);
      Object.assign(this.model, data)
    });
  },
  methods: {
    submit() {
      this.visible = false;
      let {info} = this;
      this.$api.about.contact.save(info.id, info.remark, info.state).then(res => {
        Object.assign(this.model, info)
      });
    }
  }
}
</script>