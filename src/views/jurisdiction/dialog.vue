<template>
  <el-dialog :visible.sync="visible" width="700px" custom-class="dialog-form abs-close edit-media-channel-dialog"
             @closed="$emit('closed')" :append-to-body="true" :close-on-press-escape="false"
             :close-on-click-modal="false">
    <el-form ref="form" label-width="70px" :rules="rules" v-loading="loading" size="small" style="display: flex">
      <div style="flex: 1">
        <el-form-item label="下级">
          <el-select placeholder="请选择" clearablev multiple v-model="value1">
            <el-option v-for="item in optionList" :key="item.id" :label="item.nickname" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </div>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="submit">保 存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import userRequest from "../../api/modules/userRequest";
// import MediaBase from "../media/all/base";
export default {
  props: ["value", "channels", "platform", "nodeId"],
  // extends: MediaBase,
  data() {
    let model = this.value
      ? Object.assign({}, this.value, { children: undefined })
      : {
        type: 0,
        name: "",
        pid: 0,
        sort: "",
        platform: this.platform ? 1 : 0,
        template: 0,
      };
    return {
      value1: [],
      visible: true,
      loading: false,
      model: model,
      rules: {
        name: { require: true, message: "必填项", trigger: "blur" },
      },
      nodeId: 0,
      optionList: [],
    };
  },

  created() {
    this.nodeId = this.nodeId;
    this.unassignFun();
  },
  mounted() { },
  methods: {
    unassignFun() {
      userRequest
        .get("/v1/user.account.unassign", { nodeId: this.nodeId })
        .then((res) => {
          this.optionList = res;
        })
        .catch((rej) => {
          console.log(rej);
        });
    },
    refresh() {
      this.$refs.form.refresh();
      this.needRefresh = 0;
    },
    onCreated() { },
    // refresh() {
    //   this.$refs.table.refresh();
    // },
    submit() {
      this.visible = false;
      this.$emit("setLower", this.value1);
    },
  },
};
</script>

<style lang="scss">
.edit-media-channel-dialog {
  .sort input {
    text-align: center;
  }

  .el-radio {
    width: 98px;
  }
}
</style>
