<template>
  <div>
    <mytable
      ref="table"
      :list="list"
      :api="$api.media.channel.treeList"
      :query="treeData"
      :pagination="false"
      :isRef="isRef"
    >
      <el-form class="table-toolbar" slot="toolbar">
        <div class="toolbar-header">
          <div class="toolbar-title">{{ $route.meta.title }}</div>
          <div class="toolbar-actions">
            <el-input
              class="search-val"
              size="mini"
              placeholder="请输入"
              style="width: 100px"
              maxlength="5"
              clearable
              v-model="treeData.nickname"
            >
            </el-input>
            <el-button
              size="mini"
              icon="el-icon-search"
              title="搜索"
              circle
              @click="searchFun"
            ></el-button>
          </div>
        </div>
      </el-form>

      <el-table-column label="名称" prop="nickname"></el-table-column>
      <el-table-column label="编号" prop="id"></el-table-column>
      <el-table-column label="操作" align="center" width="90px">
        <template slot-scope="scope">
          <span v-if="!platform && scope.row.platform">平台</span>
          <template v-else slot-scope="scope">
            <el-button
              class="btn-action"
              size="mini"
              @click="onEdit(scope.row)"
              title="编辑"
            >
              <svg class="icon" aria-hidden="true">
                <use
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  xlink:href="#edit"
                ></use>
              </svg>
            </el-button>
            <!-- <el-button
              class="btn-action"
              size="mini"
              @click="onDel(scope.row)"
              title="删除"
            >
              <svg class="icon" aria-hidden="true">
                <use
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  xlink:href="#delete"
                ></use>
              </svg>
            </el-button> -->
          </template>
        </template>
      </el-table-column>
    </mytable>

    <edit-dialog
      v-if="editing"
      @closed="editing = false"
      :value="model"
      :channels="channelsList"
      @change="refresh"
      :nodeId="nodeId"
      @setLower="setLower"
    ></edit-dialog>
  </div>
</template>

<script>
import EditDialog from "./dialog.vue";
import MediaBase from "../media/all/base";
import userRequest from "../../utils/userRequest";
export default {
  components: { EditDialog },
  extends: MediaBase,
  data() {
    return {
      editing: false,
      model: null,
      showSort: false,
      sortList: null,
      channelsList: [],
      value1: [],
      nodeId: 0,
    };
  },
  watch: {},
  computed: {
    list() {
      return [];
    },
    platform() {
      return this.$route.name == "platform.channel";
    },
    owner() {
      return this.$store.state.admin.id;
    },
    query() {
      return this.platform ? { platform: 1 } : { owner: this.owner };
    },
  },
  mounted() {},
  created() {
    // this.unassignFun();
  },
  methods: {
    // unassignFun() {
    //   userRequest
    //     .get("/v1/user.account.unassign")
    //     .then((res) => {
    //       this.channelsList = res;
    //     })
    //     .catch((rej) => {
    //       console.log(rej);
    //     });
    // },
    assignSubFun() {
      userRequest
        .post("/v1/user.account.assignSub", {
          pid: this.model.id,
          subIdArr: this.value1,
        })
        .then((res) => {
          this.$message({
            message: "编辑成功",
            type: "success",
          });

          this.visible = false;
          this.$router.go(0);
        })
        .catch((rej) => {
          this.$message.error("编辑失败");
        });
    },
    setLower(event) {
      this.value1 = event;
      this.assignSubFun();
    },
    onCreated() {},
    refresh() {
      this.$refs.table.refresh();
      this.needRefresh = 0;
    },
    onEdit(row) {
      console.log(row);
      this.nodeId = row.id;
      this.model = row;
      this.editing = true;
    },
    onDel(row) {
      this.$confirm("操作不可恢复，确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return this.$api.media.channel.delete(row.id);
        })
        .then(this.refresh);
    },
  },
};
</script>
<style lang="scss">
.searchBox {
  margin-bottom: 10px;
}
.searchName-val {
  margin-right: 15px;
}
.logo {
  width: 40px;
  height: 40px;
}
</style>
