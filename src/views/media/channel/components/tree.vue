<template>
  <div class="media-channel-tree" :class="{closed: closed}">
    <el-tree ref="tree" :data="list" node-key="id" current-node-key accordion highlight-current="true"
             :props="{label: 'name'}" @node-click="onChange" :indent="0">
    </el-tree>

    <div class="split-line" :title="closed ? '点击显示频道' : '点击隐藏频道'" @click.stop.prevent="toggle"></div>
  </div>
</template>

<script>
  export default {
    props: ['all', 'value'],
    data() {
      return {
        list: [],
        closed: 0
      }
    },
    created() {
      this.$api.media.channel.list({owner: this.$store.state.admin.id}).then(list => {
        this.all && list.unshift({id: 0, name: '全部'});
        this.list = list;
        this.$nextTick(_ => {
          let tree = this.$refs.tree;
          tree && tree.setCurrentKey(this.value);
        });
      });
    },
    methods: {
      onChange(v, n) {
        this.$emit('input', v.id);
        this.$emit('change', v.id, n.isLeaf);
      },
      toggle() {
        this.closed = !this.closed;
      }
    }
  }
</script>