<template>
  <div>
    <mytable ref="table" :list="list" :api="$api.media.channel.getSwipe" :query="query" :pagination="false"
             @rowClick="clickRow">
      <div class="table-toolbar" slot="toolbar">
        <div class="toolbar-header">
          <div class="toolbar-title">{{ $route.meta.title }}</div>
          <div class="toolbar-actions">
            <template>
              <el-button type="primary" size="mini" @click="upImage(event)">上传图片素材</el-button>
              <el-button type="primary" size="mini" @click="upVideo">上传视频素材</el-button>
            </template>
          </div>
        </div>
      </div>

      <el-table-column label="素材地址" align="center" prop="address" width="450px"></el-table-column>
      <el-table-column label="编号" prop="id"></el-table-column>
      <el-table-column label="排序" prop="sort" align="center"></el-table-column>
      <!-- <el-table-column label="发布时间" prop="pubdate" :formatter="fCreate" align="center"></el-table-column> -->
      <el-table-column label="状态" prop="status" align="center">
        <template scope="scope">
          <span v-if="scope.row.status === '0'">未启用</span>
          <span v-else-if="scope.row.status === '1'">已启用</span>
        </template>
      </el-table-column>
      <el-table-column label="类型" prop="type" align="center" width="90">
        <template scope="scope">
          <span v-if="scope.row.type === 'image'">图片</span>
          <span v-else-if="scope.row.type === 'video'">视频</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="150px">
        <template slot-scope="scope">
          <!-- <span v-if="!platform && scope.row.platform">平台</span> -->
          <template>
            <el-button class="btn-action" size="mini" @click.stop="onEdit(scope.row)" title="编辑">
              <svg class="icon" aria-hidden="true">
                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
              </svg>
            </el-button>
            <el-button class="btn-action" size="mini" @click.stop="onDel(scope.row)" title="删除">
              <svg class="icon" aria-hidden="true">
                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#delete"></use>
              </svg>
            </el-button>
          </template>
        </template>
      </el-table-column>
    </mytable>


    <el-dialog title='编辑素材' :visible.sync="dialogVisible" width="500px" class="edit-staff-group-dialog" @closed="onClosed"
               :append-to-body="true" :close-on-click-modal="false">

      <el-form ref="form" label-width="80px" :model="model1">
        <el-form-item label="状态" prop="status">
          <el-radio @change="statusChange" v-model="model1.status" label="1">启用</el-radio>
          <el-radio @change="statusChange" v-model="model1.status" label="0">不启用</el-radio>
          <!-- <el-input v-model.trim="model1.status" placeholder="必填项" @input="change($event)"></el-input> -->
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input v-model.number="model1.sort" placeholder="必填项" @input="changeSort($event)"
                    onkeypress='return( /[\d]/.test(String.fromCharCode(event.keyCode)))'></el-input>
        </el-form-item>
        <el-form-item label="封面" prop="poster">
          <div class="poster" @click="upImagePoster(model1)">
            上传封面图片
            <br>
            +
          </div>
          <img class="posterImg" :src="model1.cover_url" alt="" v-if="model1.cover_url">
          <img class="posterImg" src="http://cdn.dangjian.nextv.show/img/20230505/35637672/445.jpg" alt="" v-else>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSubmit1">保 存</el-button>
      </div>
    </el-dialog>

    <el-dialog title="上传视频" :visible.sync="visible" class="media-upload-video" :class="{ submitting: disabledEdit }"
               width="600px" :before-close="beforeClose" @closed="closed" :close-on-press-escape="false"
               :close-on-click-modal="false">
      <el-form ref="form" :model="model" :rules="rules" size="small" label-width="100px" auto-complete="off"
               style="margin-right:40px">
        <el-form-item label="原始文件" prop="file_url">
          <el-input v-model="model.file_url" @change="onInput" maxlength="128" class="original-url"
                    placeholder="仅支持.mp4或.m3u8结尾的文件或链接" title="外网地址可能随时无法访问，优先推荐您上传.mp4文件"></el-input>
          <div class="btn-up">
            <el-button type="primary">选择</el-button>
            <input type="file" accept=".mp4,.m3u8,.ts" multiple @change="filechanged">
          </div>
        </el-form-item>

        <el-form-item label="视频名称" prop="title">
          <el-input v-model="model.title" maxlength="64" placeholder="请输入"></el-input>
        </el-form-item>

        <el-row>
          <el-col :span="12">
            <el-form-item label="视频时长" prop="duration" title="解析后自动获取">
              <el-input :value="duration" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文件大小" prop="file_size" title="解析后自动获取">
              <el-input :value="fileSize" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item style="margin-bottom:0">
          <video ref="video" controls style="width:100%;height:236px"></video>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false" size="small" :disabled="disabledCancel">取 消</el-button>
        <el-button type="primary" @click="onSubmit" :disabled="disabledSubmit" size="small">{{ message }}</el-button>
      </div>
    </el-dialog>


    <el-dialog title='素材详情' :visible.sync="articalDialogVisible" width="1000px" class="edit-staff-group-dialog"
               @closed="onClosed1" :append-to-body="true" :close-on-click-modal="false">
      <video v-if="show" :src="src" controls autoplay width="900px" height="400px"></video>
      <img v-else :src="src" alt="" style="width: 500px;height: 400px;">
    </el-dialog>
  </div>
</template>
  
<script>
import SelectImage from '@/views/media/image/select'
export default {
  data() {
    return {
      src: {},
      sort: 1,
      editing: true,
      dialogVisible: false,
      articalDialogVisible: false,
      model1: {
        id: null,
        sort: 0,
        status: '',
        type: '',
        cover_url: ''
      },
      visible: false,
      message: '提交',
      type: '',
      isUrl: 0,
      status: '',
      model: {
        type: 'video',
        title: '',
        author: '',
        file_url: '',
        file_size: 0,
        address: [],
        duration: 0,
        width: 0,
        height: 0,
      },
      rules: {
        file_url: { required: true, message: '请填写或上传', trigger: 'blur' },
        title: { required: true, message: '必填项', trigger: 'blur' },
        duration: { type: 'number', required: true, min: 1, message: '错误', trigger: 'blur' },
        file_size: { type: 'number', required: true, min: 1, message: '错误', trigger: 'blur' },
      },
      show: false
    };
  },
  computed: {
    list() {
      return [];
    },
    duration() {
      return this.$utils.durationToTime(this.model.duration);
    },
    fileSize() {
      return this.$utils.bytesToSize(this.model.file_size);
    },
    disabledSubmit() {
      return ['success', 'wait upload', 'upload failed', 'wait submit', 'failed'].indexOf(this.status) == -1;
    },
    disabledCancel() {
      return ['parsing', 'uploading', 'submitting'].indexOf(this.status) != -1;
    },
    disabledEdit() {
      return ['parsing', 'uploading', 'submitting'].indexOf(this.status) != -1;
    }
  },
  methods: {
    clickRow(row) {
      // console.log(row);
      this.articalDialogVisible = true
      if (row.address) {
        this.src = row.address
        if (row.type == 'video') {
          this.show = true
        } else {
          this.show = false
        }
      }

    },
    statusChange(e) {
      this.model1.status = e
    },
    fCreate(row, column, val) {
      return this.$utils.Date(val).format("YYYY-MM-DD");
    },
    changeSort(e) {
      this.model1.sort = Number(e)
    },
    refresh() {
      this.$refs.table.refresh();
    },
    onEdit(row) {
      this.model1 = row;
      this.dialogVisible = true;
    },
    onDel(row) {
      this.$confirm("操作不可恢复，确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return this.$api.post('/v1/home.carousel.delete', { id: row.id });
        })
        .then(this.refresh);
    },

    onClosed() {
      this.dialogVisible = false
    },
    onClosed1() {
      this.articalDialogVisible = false
    },
    onSubmit1() {
      this.$refs.form.validate(valid => {
        if (!valid) return;


        let { model1 } = this;
        this.$api.post('/v1/home.carousel.update', model1).then(res => {
          this.dialogVisible = false;
          // console.log(res);
        }).then(this.refresh);
      });
    },
    // 创建图片轮播
    upImage() {
      SelectImage(1, {
        accept: 'image/jpeg,image/png',
        compress: 0,
      }).then((list) => {
        console.log(list);
        // this.model.image = list[0].url
        this.$api.media.createSwipe({
          type: "image",
          address: list[0].url,
          sort: this.sort,
          status: "1"
        }).then(res => {
          console.log(res);
          this.sort++
          this.refresh()
        })
      })
    },
    // 视频
    upVideo() {
      this.visible = true
    },
    beforeClose(done) {
      this.disabledCancel || done();
    },
    closed() {
      // this.$emit('closed');
      // requestAnimationFrame(() => {
      //   this.$destroy();
      //   this.$el.remove();
      // });
      this.visible = false
    },
    getProxyUrl(url) {// 视频代理
      return url;
    },
    testUrl(str) {
      return /^http(s)?:\/\/.*\.(mp4|m3u8)$/.test(str);
    },
    invalidExt() {
      this.onError('仅支持.mp4和.m3u8结尾的文件');
    },
    onError(msg) {
      this.status = 'error';
      this.$notify.error({ title: '错误', message: msg });
    },
    onInput(url) {// 手动输入原始视频播放地址
      this.files = null;
      this.isUrl = this.testUrl(url);

      Object.assign(this.model, {
        file_url: this.isUrl ? this.getProxyUrl(url) : '',
        file_size: 0,
        duration: 0
      });

      if (this.isUrl) {
        this.parseVideo(url);
      } else {
        this.parseVideo();
        this.invalidExt();
      }
    },
    parseVideo(url, type) {
      this.unload(true);
      this.$refs.form.clearValidate();

      let model = Object.assign(this.model, { width: 0, height: 0 });

      if (!url) return;

      type = type || url.substr(url.lastIndexOf('.') + 1);

      if (type != 'mp4' && type != 'm3u8') {
        return this.invalidExt();
      }

      this.type = type;
      this.status = 'parsing';
      this.message = '解析中';

      let scope = this, video = this.$refs.video;
      let removeVideo = function (err) {
        scope.unload();

        if (err) {
          scope.message = '解析失败';
          scope.onError(err);
        } else if (!scope.isUrl && model.file_size > 1024 * 1024 * 1228) {
          scope.onError('上传文件不能超过1.2G，请自行压缩！');
        } else if (model.file_size > 0 && model.duration > 0) {
          scope.status = scope.isUrl ? 'wait submit' : 'wait upload';
          scope.message = scope.isUrl ? '提交' : '上传';
        }
      }

      let onload = function () {
        Object.assign(model, { duration: this.duration, width: this.videoWidth, height: this.videoHeight });

        scope.unload();

        // 获取网络文件的大小
        if (!scope.isUrl) return removeVideo();

        scope.getFileSize(url, type).then(function (size) {
          model.file_size = size;
          removeVideo();
        }).catch(function (e) {
          removeVideo(e.message || e);
        });
      };

      video.muted = true;
      video.autoplay = true;
      video.onloadeddata = onload;
      video.oncanplaythrough = onload;
      video.onloadedmetadata = onload;
      video.onerror = function (e) {
        if (!!video.currentSrc) {
          removeVideo('若无法正确获取时长将不可上传');
        }
      };

      if (type == 'mp4') {
        video.setAttribute('src', url);
        video.play();
      } else if (type == 'm3u8') {
        this.$require('hls').then(function () {
          let hls = new Hls();
          hls.loadSource(url);
          hls.attachMedia(video);
          hls.on(Hls.Events.MANIFEST_PARSED, function () {
            isNaN(video.duration) || video.play();
          });
          this.hls = hls;
        });
      }
    },
    getMp4FileSize(url) {
      return this.$api.media.head(url).then(res => {
        if (res['content-type'].startsWith('video') || res['content-type'] == 'application/octet-stream') {
          return Number(res['content-length']);
        } else {
          return Promise.reject('文件格式错误');
        }
      });
    },
    getM3u8FileSize(url) {
      return this.$api.request({ url: url, method: 'GET', responseType: 'text' }).then(text => {
        let lines = text.split('\n')
          , prefix = url.substr(0, url.lastIndexOf('/') + 1)
          , task = []
          , exe = 0;

        for (let i = 0; i < lines.length; i++) {
          if (/.*\.ts/.test(lines[i])) {
            url = lines[i].replace(/\s/g, '');
            if (!/^http(s)?:\/\/.*\.ts$/.test(url)) {
              url = prefix + url;
            }

            task.push(this.getTsFileSize(url, () => {
              this.message = '解析中(' + (++exe) + '/' + task.length + ')';
            }));
          }
        }

        return Promise.all(task);
      }).then(values => {
        return values.reduce(function (partial, value) {
          return partial + value;
        });
      });
    },
    getTsFileSize(url, callback) {
      return this.$api.media.head(url).then(res => {
        if (res['content-type'].startsWith('video') || res['content-type'] == 'application/octet-stream') {
          return Number(res['content-length']);
        } else {
          return Promise.reject('文件格式错误');
        }
      }).finally(callback);
    },
    getFileSize(url, type) {
      switch (type) {
        case 'mp4':
          return this.getMp4FileSize(url);
        case 'm3u8':
          return this.getM3u8FileSize(url);
        case 'ts':
          return this.getTsFileSize(url);
        default:
          throw new Error('不支持的type：' + type);
      }
    },
    filechanged(e) {
      this.unload(true);

      let model = Object.assign(this.model, { duration: 0, file_size: 0 })
        , file, mp4, m3u8, tsObj = {}, ext, uplist = [], files = e.target.files;

      for (let i = 0; i < files.length; i++) {
        file = files[i];
        ext = file.name.substr(file.name.lastIndexOf('.') + 1);

        if (ext == 'mp4') {
          mp4 = file;
          break;
        } else if (ext == 'm3u8') {
          m3u8 = file;
        } else if (ext == 'ts') {
          tsObj[file.name] = file;
        }
      }

      e.target.value = '';

      this.files = uplist;
      this.isUrl = 0;

      if (mp4) {
        if (!model.title || model.title == model.file_url) model.title = mp4.name.replace('.mp4', '');
        model.file_url = mp4.name;
        model.file_size = mp4.size;
        uplist.push(mp4);
        this.parseVideo(URL.createObjectURL(mp4), 'mp4');
      } else if (m3u8) {
        if (!model.title || model.title == model.file_url) model.title = m3u8.name.replace('.m3u8', '');
        model.file_url = m3u8.name;
        model.file_size = m3u8.size;
        uplist.push(m3u8);

        let scope = this, preview = [], filename, time, reader = new FileReader(), time1 = 0, time2 = 0;
        reader.onload = function (e) {
          let lines = this.result.split('\n');

          for (let i = 0; i < lines.length; i++) {
            time = lines[i].match(/^#EXTINF:([0-9]*[.][0-9]+),$/);
            if (time) {
              time = time[1].split('.');
              time1 += parseInt(time[0]);
              time2 += parseInt(time[1]);
            }

            if (/.*\.ts/.test(lines[i])) {
              filename = lines[i];

              if (!tsObj[filename]) {
                return scope.$notify.error({ title: '错误', message: '请选择' + filename + '文件' });
              }

              file = tsObj[filename];
              model.file_size += file.size;
              uplist.push(file);

              preview.push(URL.createObjectURL(file));
              preview.push("\n");
            } else {
              preview.push(lines[i]);
              preview.push("\n");
            }
          }

          model.duration = parseInt(time2 / 1000) + '.' + (time2 % 1000);

          let temp = new File(preview, "temp.m3u8", { type: m3u8.type });
          scope.parseVideo(URL.createObjectURL(temp), 'm3u8');
        };

        reader.readAsText(m3u8);
      } else {
        this.invalidExt();
      }
    },
    onSubmit() {
      this.$refs.form.validate(valid => {
        if (!valid) return;

        switch (this.status) {
          case 'wait upload':
          case 'upload failed':
            return this.doUpload();
          case 'success':
            this.visible = false;
            return;
          case 'wait submit':
          case 'failed':
            return this.doSubmit();
        }
      });
    },
    doUpload() {
      this.status = 'uploading';
      let { model, files } = this;

      this.$api.media.upload.request({
        type: 'video',
        size: model.file_size,
        width: model.width,
        height: model.height,
        duration: model.duration
      }, files, (p) => {
        this.message = '已上传' + p + '%';
      }).then(res => {
        this.model.file_url = res.url;
        this.doSubmit();
      }).catch(e => {
        this.status = 'upload failed';
        this.message = '上传失败';
      });
    },
    doSubmit() {
      this.status = 'submitting';
      this.message = '提交中';

      let { model } = this;
      model.address = [];
      model.lower_right_corner = this.duration;

      // this.$api.media.save(model).then(media => {
      //   this.status = 'success';
      //   this.message = '成功(3s)';
      //   let index = 3;
      //   let timer = setInterval(() => {
      //     this.message = '成功(' + index + 's)';
      //     if (--index < 0) {
      //       clearInterval(timer);
      //       this.visible = false;
      //     }
      //   }, 1000);
      //   this.$emit('upload', media);
      //   console.log(media);

      // }).catch(e => {
      //   this.message = e.message || e.toString();
      //   setTimeout(() => this.status = 'failed', 3000);
      // });
      this.visible = false;
      this.$api.media.createSwipe({
        type: "video",
        address: model.file_url,
        sort: this.sort,
        status: "1"
      }).then(res => {
        console.log(res);
        this.sort++
        this.refresh()
      })
    },
    unload(clear) {
      let el = this.$refs.video;
      el.onerror = null;
      el.onloadeddata = null;
      el.oncanplaythrough = null;
      el.onloadedmetadata = null;

      if (clear) {
        el.setAttribute('src', '');

        // if (this.hls) {
        //   this.hls.destroy();
        //   this.hls = null;
        // }
      }
    },
    upImagePoster(model1) {
      // console.log(model1);
      SelectImage(1, {
        accept: 'image/jpeg,image/png',
        compress: 0,
      }).then((list) => {
        console.log(model1.cover_url, 'model1.cover_url');
        model1.cover_url = list[0].url
        // this.$api.media.createSwipe({
        //   type: "image",
        //   address: list[0].url,
        // }).then(res => {
        //   console.log(res)
        // })
      }).then(res => {
        console.log(res);
      })
    },



  },
  beforeDestroy() {
    this.sort = 1
    this.unload(true);
  },
};
</script>
<style scoped lang="scss">
.please {
  line-height: 50px;
  height: 50px;
  width: 100%;
}

.btns {
  margin-top: 50px;
  width: 100%;
  text-align: right;
}

.zanwu {
  width: 100%;
  text-align: center;
}

/deep/ .el-dialog__body {
  text-align: center !important;
}

.poster {
  width: 100px;
  height: 100px;
  border: 1px solid black;
  cursor: pointer;
  text-align: center;
}

.posterImg {
  width: 150px;
  height: 100px;
  position: absolute;
  top: 0px;
  cursor: pointer;
}
</style>