<template>
  <component v-if="!loading" :is="com" :value="media"></component>
</template>

<script>
import MediaNews from '../news/editor';
import MediaVideo from '../video/editor';
import MediaAudio from '../audio/editor';
import MediaMotto from '../motto/editor';
import MediaImage from '../image/editor';

export default {
  components: {MediaNews, MediaVideo, MediaAudio, MediaMotto, MediaImage},
  data() {
    return {
      com: null,
      media: null,
      loading: true
    }
  },
  provide() {
    return {
      '$page': this
    }
  },
  created() {
    let {params, query} = this.$route;
    this.reload(query.id, params.type, query.version);
  },
  methods: {
    reload(id, type, version) {
      this.loading = true;
      let com;

      let {query} = this.$route;

      if (query.version != version) {
        this.$router.replace({
          name: 'media.editor',
          params: {type: type},
          query: Object.assign({}, query, {id: id, version: version})
        });
      }

      switch (type) {
        case 'news':
          com = 'media-news';
          break;
        case 'video':
          com = 'media-video';
          break;
        case 'audio':
          com = 'media-audio';
          break;
        case 'motto':
          com = 'media-motto';
          break;
        case 'image':
          com = 'media-image';
          break;
        default:
          return this.$router.go(-1);
      }

      if (id && !isNaN(id)) {
        this.$api.media.get(id, type, version).then(media => {
          this.reset(com, media);
        });
      } else {
        this.reset(com);
      }
    },
    reset(com, media) {
      this.com = com;
      this.media = media;
      this.$nextTick(() => this.loading = false);
    }
  }
}
</script>
