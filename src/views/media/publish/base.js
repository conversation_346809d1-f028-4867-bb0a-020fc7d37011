import Version from "@/views/media/components/version";

export default {
  inject: ['$page'],
  props: ['value'],
  watch: {
    data(){
      return{
        sub_users:[]
      }
    },
    author: {
      immediate: true,
      handler(v) {
        if (!this.model.author) this.model.author = v;
      }
    }
  },
  computed: {
    author() {
      return this.$store.state.admin.nickname
    }
  },
  created() {
    let {model} = this;
    model.id && setTimeout(() => {
      this.md5 = this.$utils.objToMd5(this.model);
    }, 3000);
  },
  methods: {
    onSave() {
        this.validate(this.doSave).then(res => {
          this.$message.success('已存稿');
  
          this.$page.reload(res.id, res.type, res.draft);
        });
    },
    onPublish() {

        if(this.model.type == 'news' || this.model.type == 'motto'){
          this.validate(this.doSave).then(res => {
              this.$api.media.publish(res.id,res.type,res.draft).then(()=>{
                this.$store.state.admin.sub_Users = []
                this.$router.go(-1);
              })
            })
            
        }else{
          let {model} = this;
            this.$set(model,"sub_users",this.$store.state.admin.sub_Users)
            this.$api.media.save(model).then(res=>{
              this.$api.media.publish(res.id, res.type, res.draft).then(()=>{
                this.$store.state.admin.sub_Users = []
                this.$router.go(-1);
              })
            })
        }
    },
    doSave() {
      // 先判断有无子级id集合 
      if(this.$store.state.admin.sub_Users.length && !this.$store.state.admin.pid){
        let {model} = this;
        if (model.id) {
          let md5 = this.$utils.objToMd5(model);
          if (md5 == this.md5) {
            return model;
          }
        }
        this.$set(model,"sub_users",this.$store.state.admin.sub_Users)
        return this.$api.media.save(model);
      }else{
        // 没有选择下级 就走这一步
        let {model} = this;
        if (model.id) {
          let md5 = this.$utils.objToMd5(model);
          if (md5 == this.md5) {
            return model;
          }
        }
        this.$set(model,"sub_users",[])
        return this.$api.media.save(model);
      }
    },
    showVersion() {
      Version.open(this.model.id, this.model.type);
    }
  }
}