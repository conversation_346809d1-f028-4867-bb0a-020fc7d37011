<template>
  <div class="media-publish-page">
    <div>
      <div class="media-type-list">
        <div class="type-item"
             v-for="(item, index) in list"
             :class="{active: active == index, 'is-disabled': item.disabled}"
             @click="active = index"
        >{{ item.title }}
        </div>
      </div>

      <div class="media-type-action">
        <el-button v-for="btn in list[active].button" :type="btn.style" :disabled="btn.disabled" size="small" @click="onClick(btn.type)">{{ btn.text }}</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import UploadVideo from '../video/upload';
import UploadAudio from '../audio/upload';

export default {
  name: 'MediaPublishPage',
  data() {
    return {
      active: 0
    }
  },
  computed: {
    list() {
      return [{
        title: '图文',
        button: [{
          style: 'primary',
          text: '下一步',
          type: 'news'
        }]
      }, {
        title: '视频',
        button: [{
          style: 'primary',
          text: '上 传',
          type: 'video'
        }]
      }, {
        title: '音频',
        button: [{
          style: 'primary',
          text: '上 传',
          type: 'audio'
        }]
      }, {
        title: '金句',
        button: [{
          style: 'primary',
          text: '创 建',
          type: 'motto'
        }]
      }, {
        title: 'PPT',
        button: [{
          style: 'primary',
          text: '创 建',
          type: 'image'
        }]
      }]
    }
  },
  methods: {
    onClick(type) {
      switch (type) {
        case 'news':
        case 'motto':
        case 'image':
          return this.goEdit(type);
        case 'video':
          return UploadVideo().then(res => {
            this.goEdit(type, res.id, res.draf);
          });
        case 'audio':
          return UploadAudio().then(res => {
            this.goEdit(type, res.id, res.draf);
          });
      }
    },
    goEdit(type, id, version) {
      this.$router.replace({name: 'media.editor', params: {type: type}, query: {id: id, version: version}});
    }
  }
}
</script>