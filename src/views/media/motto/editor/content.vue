<template>
  <div>
    <div class="edit-panel-title">文字列表<span style="float:right">{{ total }}/{{ limit }}</span></div>
    <div class="edit-panel-desc">至少需要添加{{ min }}条，最多可添加{{ limit }}条</div>
    <div class="edit-panel-body" style="margin-top: 30px">
      <div style="padding:0 15px;height:70px">
        <el-slider v-model="model.interval" :min="3" :max="15" :step="1" :marks="marks" :format-tooltip="formatInterval"></el-slider>
      </div>

      <div ref="list" style="max-height:calc(100vh - 150px);overflow-y:auto"></div>
      <el-empty v-if="total==0" label="当前未添加任何文字">
        <el-button type="primary" size="small" @click="onAdd">添加</el-button>
      </el-empty>
      <div v-else style="text-align: right" v-show="list.length < limit" @click="onAdd">
        <el-button type="text">添加</el-button>
      </div>
    </div>

    <el-dialog
        title="请输入文字后按回车关闭"
        :center="true"
        :visible.sync="dialogVisible"
        width="450px"
        :append-to-body="true"
        clearable>
      <el-input v-model="input" type="textarea" :rows="6" maxlength="200" show-word-limit @keydown.native="onInput"></el-input>
    </el-dialog>
  </div>
</template>

<script>
import ElEmpty from '@/components/empty/view';

export default {
  components: {ElEmpty},
  data() {
    return {
      min: 3,
      total: 0,
      limit: 50,
      dialogVisible: false,
      input: '',
      current: -1,
      marks: {
        3: '快',
        9: '中',
        15: '慢',
      }
    }
  },
  computed: {
    model() {
      return this.$parent.model;
    },
    list() {
      return this.model.content;
    }
  },
  mounted() {
    this.list.forEach(str => this.append(str));

    this.$require('sortable').then(() => {
      new Sortable(this.$refs.list, {});
    });
  },
  methods: {
    validate(callback) {
      let nodes = this.$refs.list.children, len = nodes.length;

      if (len < 3) {
        return this.$message.error('请至少添加3条');
      }

      this.list.length = 0;

      for (let i = 0; i < len; i++) {
        this.list.push(nodes[i].childNodes[0].textContent);
      }

      this.total = this.list.length;
      callback();
    },
    append(str) {
      let container = document.createElement('div');
      container.className = 'media-motto-item ellipsis';

      let text = document.createTextNode(str);
      container.appendChild(text);

      let action = document.createElement('div');
      action.className = 'action';
      action.innerHTML = '<i class="el-icon-edit"></i>';
      container.appendChild(action);
      action.onclick = (e) => {
        e.preventDefault();
        e.stopPropagation();

        let el = container, nodes = el.parentNode.childNodes, i = 0, len = nodes.length;
        while (i < len) {
          if (nodes[i] === el) {
            break;
          }
          i++;
        }

        this.current = i;
        this.input = str;
        this.dialogVisible = true;
      }

      this.total++;
      this.$refs.list.appendChild(container);
    },
    remove(i) {
      this.total--;
      this.$refs.list.children[i].remove();
    },
    modify(i, str) {
      this.$refs.list.children[i].childNodes[0].textContent = str;
    },
    onAdd() {
      this.current = -1;
      this.input = '';
      this.dialogVisible = true;
    },
    onInput(e) {
      if (e.keyCode === 13) {
        e.preventDefault();
        e.stopPropagation();

        this.dialogVisible = false;
        let str = this.input.replace(/\s/g, '');
        let i = this.current;

        if (i == -1) {
          !!str && this.append(str);
        } else if (!!str) {
          this.modify(i, str);
        } else {
          this.remove(i);
        }
      }
    },
    formatInterval(v) {
      return '间隔' + v + '秒一切换'
    }
  }
}
</script>