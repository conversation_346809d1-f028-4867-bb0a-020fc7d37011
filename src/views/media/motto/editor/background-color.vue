<template>
  <div class="edit-base">
    <div class="edit-panel-title">背景颜色</div>
    <div class="edit-panel-desc">选择一个契合背景图片边缘的颜色，用于遮挡超出图片宽高区域</div>

    <div class="edit-panel-body">
      <el-color-picker
          ref="color"
          v-model="model.bgcolor"
          show-alpha
          popper-class="media-color-panel is-hidden"
          :predefine="predefineColors">
      </el-color-picker>
    </div>
  </div>
</template>

<style lang="scss">
.media-color-panel {
  width: 100%;
  border: none;
  padding: 0;
  box-shadow: none;
  position: relative !important;
  left: 0 !important;
  top: 0 !important;

  &.is-hidden {
    visibility: hidden;
    touch-action: none;
    pointer-events: none;
  }

  .el-color-svpanel {
    width: calc(100% - 20px);
    height: 260px;
  }

  .el-color-hue-slider.is-vertical {
    height: 260px;
  }

  .el-color-alpha-slider, .el-color-predefine {
    width: 100%;
  }

  .el-color-predefine__color-selector + .el-color-predefine__color-selector {
    margin-left: 8px !important;
  }

  .el-color-dropdown__value {
    width: 180px;
    float: none;
  }

  .el-input__inner {
    text-align: center;
  }

  .el-color-dropdown__btns .el-button {
    display: none;
  }
}
</style>

<script>
export default {
  data() {
    return {
      predefineColors: [
        '#000000',
        '#333333',
        '#ff4500',
        '#ff8c00',
        '#ffd700',
        '#90ee90',
        '#00ced1',
        '#1e90ff',
        '#c71585',
        'rgb(255, 120, 0)',
        'rgba(255, 69, 0, 0.68)'
      ]
    }
  },
  computed: {
    model() {
      return this.$parent.model
    }
  },
  mounted() {
    let $color = this.$refs.color;
    let $dropdown = $color.$refs.dropdown;
    let $el = $dropdown.$el;

    $el.style.cssText = 'display:block;position:relative;';
    $el.classList.remove('is-hidden');
    $color.$el.parentElement.appendChild($el);
    $color.$el.remove();

    $dropdown.$watch('customInput', (v) => {
      this.model.bgcolor = v;
    });
  },
  methods: {
    validate(callback) {
      callback();
    }
  }
}
</script>