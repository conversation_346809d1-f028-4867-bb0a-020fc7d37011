<template>
  <div class="edit-base">
    <div class="edit-panel-title">背景音乐</div>
    <div class="edit-panel-desc"></div>
    <div class="edit-panel-body" style="margin-top: 40px;padding: 30px 20px;display: flex;align-items: center;background-color:#f5f7fa">
      <div style="width: 200px;height: 200px;margin: auto;border-radius: 50%;overflow: hidden;display: flex;align-items: center;justify-content: center;">
        <el-image :src="cover" fit="cover" style="width: 80%;height:80%;" :class="{'motto-rotate-music' : playing}">
          <template slot="error">&nbsp;</template>
        </el-image>
        <div style="background:no-repeat url(https://cdn.nextv.show/sys/albumcover.png) 100% 100%/cover;width: 100%;height: 100%;position: absolute;left: 0;top: 0;"></div>
        <div style="position: absolute;color: rgb(255, 255, 255);font-size: 45px;cursor: pointer" @click="togglePlay">
          <svg class="icon" aria-hidden="true">
            <use :xlink:href="`#play${playing ? 'ing' : ''}-border`"></use>
          </svg>
        </div>
      </div>
      <el-form label-width="90px" label-suffix="：" size="small" style="flex:1;padding-left:10px">
        <el-form-item label="播放地址" class="media-motto-upbgmusic">
          <el-input v-model="model.url" placeholder="请输入" size="small" maxlenght="128" @change="onChange"></el-input>
          <el-button type="text" @click="uploadAudio">选择</el-button>
        </el-form-item>
        <el-form-item label="音乐名称">
          <el-input v-model="model.name" placeholder="请输入" size="small" clearable maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="默认音量" style="padding-right:10px">
          <el-slider v-model="model.volume" :min="0.1" :max="1" :step="0.1" :format-tooltip="formatVolume"></el-slider>
        </el-form-item>
        <el-form-item label="音乐设置" style="margin-bottom:0">
          <el-switch v-model="model.loop" active-text="循环" :active-value="1" :inactive-value="0"></el-switch>
          <el-switch v-model="model.enabled" active-text="启用" :active-value="1" :inactive-value="0" style="margin-left:30px"></el-switch>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import SelectAudio from '@/views/media/audio/select';

export default {
  data() {
    return {
      playing: false
    }
  },
  computed: {
    model() {
      return this.$parent.model.bgmusic
    },
    cover() {
      return this.$parent.model.cover_url
    },
    audio() {
      let el = document.createElement('audio');
      el.crossOrigin = 'anonymous';
      el.onerror = () => this.playing = false;
      el.onpause = () => this.playing = 0;
      el.onplay = () => this.playing = 1;
      return el;
    }
  },
  watch: {
    'model.volume': {
      immediate: true,
      handler(v) {
        this.audio.volume = v;
      }
    },
    'model.loop': {
      immediate: true,
      handler(v) {
        this.audio.loop = v;
      }
    }
  },
  beforeDestroy() {
    this.audio.src = '';
  },
  deactivated() {
    this.audio.pause();
  },
  methods: {
    validate(callback) {
      callback();
    },
    formatVolume(v) {
      return v == 0 ? '静音' : v == 1 ? '最大' : (v * 100 + '%');
    },
    uploadAudio() {
      SelectAudio(1).then(list => {
        let item = list[0];

        Object.assign(this.model, {
          url: item.address,
          name: item.title
        });

        this.onChange();
      });
    },
    togglePlay() {
      if (this.playing === false) {
        this.audio.src = this.model.url;
      }

      this.playing ? this.audio.pause() : this.audio.play();
    },
    onChange() {
      this.audio.src = '';
      this.playing = false;
    }
  }
}
</script>