<!-- <template>
    <el-dialog
        :title="model.id ? '编辑通知': '创建通知'"
        :visible.sync="dialogVisible"
        width="500px"
        class="edit-staff-group-dialog"
        @closed="onClosed"
        :append-to-body="true">
  
      <el-form ref="form" label-width="80px" :model="model" :rules="rules">
        <el-form-item label="标题" prop="notice_title">
          <el-input v-model.trim="model.notice_title" maxlength="15" placeholder="必填项"></el-input>
        </el-form-item>
        <el-form-item label="正文" prop="notice_content">
              <el-input
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4}"
              placeholder="请输入正文"
              v-model="model.notice_content">
            </el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
            <el-input v-model.trim="model.remark"  placeholder=""></el-input>
        </el-form-item>
      </el-form>
  
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSubmit">保 存</el-button>
      </div>
    </el-dialog>
  </template>
  
  <script>
  export default {
    props: ['visible', 'editing'],
    data() {
      return {
        dialogVisible: false,
        model: {
            id:null,
            notice_title: '',
            notice_content: '',
            remark: ""
        },
        rules: {
            notice_title: {required: true, message: '必填项'},
        }
      }
    },
    watch: {
      visible: {
        immediate: true,
        handler(v) {
          this.dialogVisible = v;
        }
      },
      editing(v) {
        let form = this.$refs.form;
        form && form.resetFields();
        Object.assign(this.model, v);
      }
    },
    methods: {
      onClosed() {
        this.$emit('update:visible', false)
      },
      onSubmit() {
        this.$refs.form.validate(valid => {
          if (!valid) return;
  
          this.dialogVisible = false;
  
          let {model} = this;
          this.$api.post('/v1/sys.notice.create', model).then(_ => {
            this.$emit('change');
          });
        });
      }
    }
  }
  </script> -->