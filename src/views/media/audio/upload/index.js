import Dialog from './dialog.vue';

let View, view;

export default function () {
  return new Promise(function (resolve, reject) {
    if (!View) View = Vue.extend(Dialog);

    let media, el = document.createElement('div');
    document.body.appendChild(el);

    view = new View({el: el});

    view.$once('upload', (res) => media = res);
    view.$once('closed', () => {
      view = null;
      media ? resolve(media) : reject();
    });
    view.visible = true;
  });
}