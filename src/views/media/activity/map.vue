<template>
  <div>
    <el-dialog
      width="60%" top="50px"
      :before-close="cancel"
      :closable="false"
      :mask-closable="false"
      :visible="visible"
      :close-on-click-modal="false"
      v-if="visible"
    >
      <span>
       <!-- <el-amap-search-box
          class="search-box"
          :search-option="searchOption"         
          :on-search-result ="querySearch"   
      > </el-amap-search-box> -->
        <div id="amap-container" style="width:100%;height:500px;">
          <el-amap class="amap-box" :vid="'amap-vue'" :center="center" :plugin="plugin" :events="events" :zoom="zoom">
            <el-amap-marker :position="center"></el-amap-marker>
              <el-amap-marker
              v-for="(marker,index) in markersArray"
              :key ="'marker'+index"
              :position ="marker">
              </el-amap-marker>
          </el-amap>
        </div>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import VueAMap from 'vue-amap';
Vue.use(VueAMap);

export default {
    name: "AMap",
    components: {},
    data () {
        return {
          mapApiKey:"3a59c1374b53393d8f88a2fb607082c9", //您申请的key值
          markersArray: [106.797248,26.642313],
          zoom: 15,
          lnglatInfo: {
              lng: '',
              lat: '',
              addr: ''
          },
          searchOption: {
                city: "全国",
                citylimit: false,
          },
          center: [106.797248,26.642313],
          plugin: [
            {
              pName: 'Scale',
              events: {
                init(instance) {
                  // console.log(instance)
                }
              }
            },
            {
              pName: 'ToolBar',
              position:'RT',
              events: {
                init(instance) {
                  // console.log(instance)
                }
              }
            }
          ]
          ,events: {
            init(o){
              // console.log(o.getCenter());
            },
            zoomchange: (e) => {
                console.log(e);
            },  
            zoomend: (e) => {
                //获取当前缩放zoom值
                // console.log(this.$refs.map.$$getInstance().getZoom());
                console.log(e);
            },
            click: e => {
            //地图中的点击事件
              this.center =  [e.lnglat.lng,e.lnglat.lat];
              this.markersArray = [];
              this.markersArray.push(this.center)
              this.lnglatInfo.lat = e.lnglat.lat;
              this.lnglatInfo.lng = e.lnglat.lng;
            }
          },
      }
  },
  props: {
    visible: Boolean,
    centerEdit: Array
  },
  created () {},
  watch: {},
  mounted() {
    this.init()
    this.getLocation(); // 调用获取地理位置
  },
  created(){

  },
  methods: {
    // 初始化地图
    init() {
        let _self =this;
        VueAMap.initAMapApiLoader({
          key: _self.mapApiKey,
          plugin: ['AMap.Autocomplete', 'AMap.PlaceSearch', 'AMap.Scale', 'AMap.OverView', 'AMap.ToolBar', 'AMap.MapType', 'AMap.PolyEditor', 'AMap.CircleEditor','AMap.Geocoder'],
          // 高德 sdk 版本
          v: '1.4.15'
      });
    }, 
   
    //点击搜索后触发的事件
      querySearch(pois){
        this.input = document.querySelector('.search-box-wrapper input').value 
        this.center = [pois[0].lng,pois[0].lat]        //选择了第一个值
        this.markersArray = [];    //标记点先清空  
        this.markersArray.push([pois[0].lng,pois[0].lat])   //把搜索的位置的第一个值存入标记中并显示标记点
        this.lnglatInfo.lat = pois[0].lat;
        this.lnglatInfo.lng = pois[0].lng;
      },
    /***
     * 确认，并返回选择的经纬度
     */
    confirm: function () {
      this.$emit('map-confirm', this.lnglatInfo)
    },
    /***
     * 取消
     */
    cancel: function () {
      this.$emit('cancel')
    },
    // 获取当前定位
    getLocation(){
      let that = this

      

            let aMapScript = document.createElement('script');
            aMapScript.setAttribute('src', 'https://webapi.amap.com/maps?v=1.4.11&key=43e0bc7d5e563a10819cd13deb9f8eb7&plugin=AMap.CitySearch');
            aMapScript.onload = () => {
                AMap.plugin(['AMap.Geolocation', 'AMap.Geocoder'], () => {
                    const geolocation = new AMap.Geolocation({
                        enableHighAccuracy: true,
                        timeout: 5000,
                        zoomToAccuracy: true,
                        showButton: true,
                        buttonPosition: 'LB',
                        panToLocation: true,
                        GeoLocationFirst: true
                        // enableHighAccuracy: true,
                        // timeout: 10000,
                        // buttonOffset: new AMap.Pixel(10, 20),
                        // zoomToAccuracy: true,
                        // buttonPosition: 'RB'
                    });
                    const geocoder = new AMap.Geocoder();
                    geolocation.getCurrentPosition();
                    AMap.event.addListener(geolocation, 'complete', (data) => {
                        console.log(data);
                        const lnglat = [data.position.lng, data.position.lat];
                        that.center = lnglat
                        // geocoder.getAddress(lnglat, (status, result) => {
                        //     if (status === 'complete' && result.info === 'OK') {
                        //         const addressComponents = result.regeocode.addressComponent;
                        //         const province = addressComponents.province;
                        //         const city = addressComponents.city;
                        //         const district = addressComponents.district;
                        //         this.city = province
                        //         this.cityMarket = city || district
                        //         console.log('省:', province);
                        //         console.log('市:', city);
                        //         console.log('区:', district);
                        //     } else {
                        //         console.log('逆地理编码失败:', result);
                        //     }
                        // });
                    });
                    AMap.event.addListener(geolocation, 'error', (data) => {
                        console.log(data);
                        console.log('定位失败');
                    });
                });
            };
            document.head.appendChild(aMapScript);
    },
   
  }
}
</script>

<style lang="scss" scoped>
.serachinput {
  width: 300px;
  box-sizing: border-box;
  padding: 9px;
  border: 1px solid #dddee1;
  line-height: 20px;
  font-size: 16px;
  height: 38px;
  color: #333;
  position: relative;
  border-radius: 4px;
  -webkit-box-shadow: #666 0px 0px 10px;
  -moz-box-shadow: #666 0px 0px 10px;
  box-shadow: #666 0px 0px 10px;
}
::v-deep .el-dialog__header {
  border-bottom: 0 !important;
}
.search-box{
  width:100%;
  height:40px;
}
::v-deep .el-vue-search-box-container .search-box-wrapper .search-btn{
  background-color: #409eff;
  border-color: #409eff;
  color:#fff;
  width: 70px;
  border-radius: 4px;
}
::v-deep .el-vue-search-box-container .search-box-wrapper input{
  height: 40px;
  line-height: 40px;
  border: 1px solid #dcdfe6;
}
</style>
