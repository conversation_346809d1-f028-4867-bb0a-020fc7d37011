<template>
    <div>
      <mytable ref="table" :list="list" :api="$api.media.channel.getActivityList" :query="query" :pagination="true">
        <div class="table-toolbar" slot="toolbar">
          <div class="toolbar-header">
            <div class="toolbar-title">{{ $route.meta.title }}</div>
            <div class="toolbar-actions">
              <template>
                <el-button type="primary" size="mini" @click="add">添加</el-button>
              </template>
            </div>
          </div>
        </div>
  
        <el-table-column label="活动名称" prop="activity_name" align="center" ></el-table-column>
        <el-table-column label="活动地点" prop="activity_address" align="center" ></el-table-column>
       
        <el-table-column label="操作" align="center" width="150px">
          <template slot-scope="scope">
            <template>
              <el-button class="btn-action" size="mini" @click.stop="onEdit(scope.row)" title="编辑">
                <svg class="icon" aria-hidden="true">
                  <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
                </svg>
              </el-button>
              <el-button class="btn-action" size="mini" @click.stop="onDel(scope.row)" title="删除">
                <svg class="icon" aria-hidden="true">
                  <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#delete"></use>
                </svg>
              </el-button>
            </template>
          </template>
        </el-table-column>
      </mytable>
  
  
      <el-dialog title='编辑' class="media-upload-video" :visible.sync="dialogVisible" width="800px" :before-close="beforeClose" @closed="closed" :close-on-press-escape="false"
          :close-on-click-modal="false">
        <el-form ref="form" label-width="150px" :model="model1"  auto-complete="off"
        style="margin-right:40px">
          <el-form-item label="活动名称" required>
            <el-input v-model="model1.activity_name" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="活动时间" required>
            <el-date-picker type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 100%;" v-model="model1.activity_date" value-format="yyyy-MM-dd HH:mm:ss" ></el-date-picker>
          </el-form-item>
          <el-form-item label="活动地点" required>
            <el-input v-model="model1.activity_address" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="活动时长" required>
            <el-input v-model="model1.activity_duration" placeholder="请输入"></el-input>
          </el-form-item>

          <el-row>
            <el-col :span="12">
              <el-form-item label="报名截止时间" required>
            <el-date-picker type="datetime" placeholder="选择时间" style="width: 100%;" v-model="model1.dead_line" value-format="timestamp"></el-date-picker>
          </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="招募人数" required>
            <el-input v-model="model1.people_num" placeholder="人" type="number"></el-input>
          </el-form-item>
            </el-col>
          </el-row>

          
          
          <el-row>
            <el-col :span="12">
              <el-form-item label="联系人" required>
                <el-input v-model="model1.people_name" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系电话" required> 
                <el-input v-model="model1.people_mobile" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          
          
          <el-form-item label="活动地址定位" required>
            <!-- <el-input v-model="model1.position" placeholder="请输入"></el-input> -->
            <div style="display: flex;">
              <el-input v-model="model1.position" disabled></el-input>
              <el-button type="primary" @click="showAMap">打开地图</el-button>
            </div>
          </el-form-item>
          <el-form-item label="活动封面照片" required>
            <div class="poster" @click="upImagePoster(model1)" style="border: 1px solid #ccc;">
              上传(图片尺寸建议 : 350px*182px)
              <br>
              +
            </div>
            <img class="posterImg" :src="model1.activity_poster" alt="" v-if="model1.activity_poster">
          </el-form-item>
          <el-form-item label="活动内容" required>
            <el-input type="textarea" v-model="model1.content" :rows="6" maxlength="-1"
                  placeholder="请输入">
            </el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="onSubmit1">保 存</el-button>
        </div>
      </el-dialog>
  
      <el-dialog title="发布" :visible.sync="visible" class="media-upload-video"
                 width="700px" :before-close="beforeClose" @closed="closed" :close-on-press-escape="false"
                 :close-on-click-modal="false">
        <el-form ref="forms" :model="model" :rules="rules"  size="small" label-width="150px" auto-complete="off"
                 style="margin-right:40px">
          <el-form-item label="活动名称" prop="activity_name">
            <el-input v-model="model.activity_name" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="活动时间" prop="activity_date">
            <el-date-picker type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 100%;" v-model="model.activity_date" value-format="yyyy-MM-dd HH:mm:ss" ></el-date-picker>
          </el-form-item>
          <el-form-item label="活动地点" prop="activity_address">
            <el-input v-model="model.activity_address" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="活动时长" prop="activity_duration">
            <el-input v-model="model.activity_duration" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="报名截止时间" prop="dead_line">
            <el-date-picker type="datetime" placeholder="选择时间" style="width: 100%;" v-model="model.dead_line" value-format="timestamp"></el-date-picker>
          </el-form-item>
          <el-form-item label="招募人数" prop="people_num">
            <el-input v-model="model.people_num" placeholder="人" type="number"></el-input>
          </el-form-item>
          <el-form-item label="联系人" prop="people_name">
            <el-input v-model="model.people_name" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="联系电话" prop="people_mobile">  
            <el-input v-model="model.people_mobile" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="活动地址定位" prop="position" >
            <div style="display: flex;">
              <el-input v-model="model.position" disabled></el-input>
              <el-button type="primary" @click="showAMap">打开地图</el-button>
            </div>
          </el-form-item>
          <el-form-item label="活动封面照片" prop="activity_poster">
            <div class="poster" @click="upImagePoster(model)" style="border: 1px solid #ccc;">
              上传(图片尺寸建议 : 350px*182px)
              <br>
              +
            </div>
            <img class="posterImg" :src="model.activity_poster" alt="" v-if="model.activity_poster">
          </el-form-item>
          <el-form-item label="活动内容" prop="content">
            <el-input type="textarea" v-model="model.content" :rows="6" maxlength="-1"
                  placeholder="请输入">
            </el-input>
          </el-form-item>
        </el-form>
  
        <div slot="footer" class="dialog-footer">
          <el-button @click="visible = false" size="small" >取 消</el-button>
          <el-button type="primary" @click="onSubmit"  size="small">{{ message }}</el-button>
        </div>
      </el-dialog>

      <AMap @cancel="showMap = false" @map-confirm="confirmLocation" :visible="showMap" :centerEdit="center" ></AMap>
    </div>
  </template>
    
  <script>
import SelectImage from '@/views/media/image/select'
import AMap from "./map.vue";
  export default {
    components:{AMap},
    data() {
      return {
        src: {},
        sort: 1,
        editing: true,
        dialogVisible: false,
        model1: {},
        visible: false,
        message: '提交',
        type: '',
        isUrl: 0,
        status: '',
        model: {},
        show: false,
        query:{
        },
        rules: {
          activity_name: [{ required: true, message: '请输入活动名称', trigger: 'blur' }],
          activity_date: [{ required: true, message: '请输入活动时间', trigger: 'blur' }],
          activity_address: [{ required: true, message: '请输入活动地点', trigger: 'blur' }],
          activity_duration: [{ required: true, message: '请输入活动时长', trigger: 'blur' }],
          dead_line: [{ required: true, message: '请输入截止时间', trigger: 'blur' }],
          people_num: [{ required: true, message: '请输入招募人数', trigger: 'blur' }],
          people_name: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
          people_mobile: [{ required: true, message: '请输入联系人电话', trigger: 'blur' }],
          position: [{ required: true, message: '请输入活动地址定位', trigger: 'blur' }],
          activity_poster: [{ required: true, message: '请上传封面图片', trigger: 'blur' }],
          content: [{ required: true, message: '请输入活动内容', trigger: 'blur' }],
        },

        showMap:false,
        center:[]
      };
    },
    mounted() {
        
    },
    computed: {
      list() {
        return [];
      },
    },
    methods: {
      add(){
        this.visible = true
      }, 
      refresh() {
        this.$refs.table.refresh();
      },
      onEdit(row) {
        console.log(row);
        if(row.activity_date && Object.prototype.toString.call(row.activity_date) == '[object String]' ){
          row.activity_date = row.activity_date.split(',')
        }
        row.dead_line = row.dead_line * 1000
        this.model1 = row;
        this.dialogVisible = true;
      },
      onDel(row) {
        this.$confirm("操作不可恢复，确定删除吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            return this.$api.post('/v1/media.activity.delete', { id: row.id });
          })
          .then(this.refresh);
      },
  
      onSubmit() {
        let time = this.model.activity_date.join(',')
        this.model.activity_date = time
        this.model.created =  this.model.dead_line / 1000
        this.model.dead_line =  this.model.dead_line / 1000

        console.log(this.model);
        this.$refs.forms.validate(valid => {
          if (!valid) return;
  
          let { model } = this;
          this.$api.post('/v1/media.activity.create', model).then(res => {
            this.visible = false;
            // console.log(res);
          }).then(this.refresh);
        });
      },
      onSubmit1() {
          let time = this.model1.activity_date.join(',')
          this.model1.activity_date = time
          this.model1.dead_line =  this.model1.dead_line / 1000
          console.log(this.model1);
          let { model1 } = this;
          this.$api.post('/v1/media.activity.update', model1).then(res => {
            this.dialogVisible = false;
            // console.log(res);
          }).then(this.refresh);
      },
      upImagePoster(model) {
        let that = this
        SelectImage(1, {
          accept: 'image/jpeg,image/png',
          compress: 0,
        }).then((list) => {
          model.activity_poster = list[0].url
          console.log(model.activity_poster,model, 'model.activity_poster');
          that.$forceUpdate()

        }).then(res => {
          console.log(res);
        })
      },
      confirmLocation(e) {
            // this.latlng.lat = e.lat
            // this.latlng.lng = e.lng
            console.log(e);
            if(this.visible){
              this.model.position =  e.lng + "," + e.lat

            }else{
              this.model1.position = e.lng + "," + e.lat

            }
            this.showMap = false
        },
        showAMap(){
            this.showMap = true;

            if(!this.visible){
              this.center = this.model1.position.split(',')
            }
        }
      
   
      
  
  
  
    },
  };
  </script>
  <style scoped lang="scss">
  .please {
    line-height: 50px;
    height: 50px;
    width: 100%;
  }
  
  .btns {
    margin-top: 50px;
    width: 100%;
    text-align: right;
  }
  
  .zanwu {
    width: 100%;
    text-align: center;
  }
  
  /deep/ .el-dialog__body {
    text-align: center !important;
  }
  
  .poster {
    width: 250px;
    height: 100px;
    border: 1px solid black;
    cursor: pointer;
    text-align: center;
  }
  
  .posterImg {
    width: 175px;
    height: 91px;
    position: absolute;
    top: 5px;
    cursor: pointer;
  }
  </style>