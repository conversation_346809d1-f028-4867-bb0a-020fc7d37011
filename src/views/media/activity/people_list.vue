<template>
    <div>
      <mytable ref="table" :list="list" :api="$api.media.channel.getSignUpList" :query="query" :pagination="true">
        <div class="table-toolbar" slot="toolbar">
          <div class="toolbar-header">
            <div class="toolbar-title">{{ $route.meta.title }}</div>
            <!-- <div class="toolbar-actions">
              <template>
                <el-button type="primary" size="mini" @click="add">添加</el-button>
              </template>
            </div> -->
          </div>
        </div>
  
        <el-table-column label="活动名称" prop="activity_name" align="center" ></el-table-column>
        <el-table-column label="报名人" prop="user_name" align="center" ></el-table-column>
       
        <el-table-column label="操作" align="center" width="150px">
          <template slot-scope="scope">
            <template>
              <!-- <el-button class="btn-action" size="mini" @click.stop="onEdit(scope.row)" title="编辑">
                <svg class="icon" aria-hidden="true">
                  <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
                </svg>
              </el-button> -->
              <el-button class="btn-action" size="mini" @click.stop="onDel(scope.row)" title="删除">
                <svg class="icon" aria-hidden="true">
                  <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#delete"></use>
                </svg>
              </el-button>
            </template>
          </template>
        </el-table-column>
      </mytable>
  
    </div>
  </template>
    
  <script>
  export default {
    data() {
      return {
        src: {},
        sort: 1,
        editing: true,
        dialogVisible: false,
        model1: {},
        visible: false,
        message: '提交',
        type: '',
        isUrl: 0,
        status: '',
        model: {},
        show: false,
        query:{
        },
        showMap:false,
        center:[]
      };
    },
    mounted() {
        let  t = new Date('2024-10-14 00:09:00').getTime()
        console.log(t);
    },
    computed: {
      list() {
        return [];
      },
    },
    methods: {
      add(){
        this.visible = true
      }, 
      refresh() {
        this.$refs.table.refresh();
      },
    //   onEdit(row) {
    //     console.log(row);
    //     if(row.activity_date && Object.prototype.toString.call(row.activity_date) == '[object String]' ){
    //       row.activity_date = row.activity_date.split(',')
    //     }
    //     row.dead_line = row.dead_line * 1000
    //     this.model1 = row;
    //     this.dialogVisible = true;
    //   },
      onDel(row) {
        this.$confirm("操作不可恢复，确定删除吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            return this.$api.post('/v1/media.activity.destroySignUp', { id: row.id });
          })
          .then(this.refresh);
      },
  
      onSubmit() {
        let time = this.model.activity_date.join(',')
        this.model.activity_date = time
        this.model.created =  this.model.dead_line / 1000
        this.model.dead_line =  this.model.dead_line / 1000

        console.log(this.model);
        this.$refs.forms.validate(valid => {
          if (!valid) return;
  
          let { model } = this;
          this.$api.post('/v1/media.activity.create', model).then(res => {
            this.visible = false;
            // console.log(res);
          }).then(this.refresh);
        });
      },
      onSubmit1() {
          let time = this.model1.activity_date.join(',')
          this.model1.activity_date = time
          this.model1.dead_line =  this.model1.dead_line / 1000
          console.log(this.model1);
          let { model1 } = this;
          this.$api.post('/v1/media.activity.update', model1).then(res => {
            this.dialogVisible = false;
            // console.log(res);
          }).then(this.refresh);
      },
      upImagePoster(model) {
        let that = this
        SelectImage(1, {
          accept: 'image/jpeg,image/png',
          compress: 0,
        }).then((list) => {
          model.activity_poster = list[0].url
          console.log(model.activity_poster,model, 'model.activity_poster');
          that.$forceUpdate()

        }).then(res => {
          console.log(res);
        })
      },
      confirmLocation(e) {
            // this.latlng.lat = e.lat
            // this.latlng.lng = e.lng
            console.log(e);
            if(this.visible){
              this.model.position =  e.lng + "," + e.lat

            }else{
              this.model1.position = e.lng + "," + e.lat

            }
            this.showMap = false
        },
        showAMap(){
            this.showMap = true;

            if(!this.visible){
              this.center = this.model1.position.split(',')
            }
        }
      
   
      
  
  
  
    },
  };
  </script>
  <style scoped lang="scss">
  .please {
    line-height: 50px;
    height: 50px;
    width: 100%;
  }
  
  .btns {
    margin-top: 50px;
    width: 100%;
    text-align: right;
  }
  
  .zanwu {
    width: 100%;
    text-align: center;
  }
  
  /deep/ .el-dialog__body {
    text-align: center !important;
  }
  
  .poster {
    width: 100px;
    height: 100px;
    border: 1px solid black;
    cursor: pointer;
    text-align: center;
  }
  
  .posterImg {
    width: 150px;
    height: 100px;
    position: absolute;
    top: 0px;
    cursor: pointer;
  }
  </style>