<template>
  <div class="edit-panel-page edit-media-panel">
    <div class="edit-panel-box">
      <div class="edit-panel-side">
        <div class="edit-panel-name">图文素材</div>

        <media-image
          v-model="model.cover_url"
          style="margin-top: 30px; width: 100%; height: 140px"
        ></media-image>

        <div class="edit-panel-menu">
          <div
            class="item"
            v-for="item in menu"
            :class="{ active: active == item.name }"
            @click="go(item.name)"
          >
            <div class="label">{{ item.label }}</div>
            <div class="value">
              {{ item.value }}<i class="el-icon-arrow-right"></i>
            </div>
          </div>
        </div>

        <div class="edit-panel-submit">
          <el-button size="small" @click="onSave">存 稿</el-button>
          <el-button type="primary" size="small" @click="onPublish"
            >发 布</el-button
          >
        </div>
      </div>
      <div class="edit-panel-content">
        <keep-alive>
          <component ref="part" v-bind:is="`edit-${active}`"></component>
        </keep-alive>
        <div class="edit-panel-action">
          <el-button size="small" @click="prev()" v-show="current > 0"
            >上一步</el-button
          >
          <el-button
            type="primary"
            size="small"
            v-show="current < menu.length - 1"
            @click="next()"
            >下一步</el-button
          >
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.edit-media-panel {
  .show-channel {
    display: flex;
    padding: 30px 0;

    &:hover .channel-action {
      display: block;
    }

    .upload-cover-image {
      width: 300px;
      height: 168px;
    }
  }

  .required-channel:before {
    content: "*";
    color: red;
    position: absolute;
    top: 0;
    left: -0.5em;
  }

  .sort-channel {
    position: absolute;
    left: 0;
    bottom: 0;
    font-size: 13px;

    input {
      border: none;
      border-bottom: 1px solid #409eff;
      width: 80px;
      text-align: center;
    }
  }

  .channel-action {
    cursor: pointer;
    position: absolute;
    right: -6px;
    bottom: -4px;
    font-size: 12px;
    display: none;
    padding: 4px 6px;

    &:hover {
      color: #5cb6ff;
    }
  }

  .channel-error {
    color: red;
    float: right;
  }

  .media-channel-cascader {
    width: 100%;
  }
}
</style>

<script>
import Base from "../../publish/panel";
import MediaImage from "@/views/media/image/cover";
import EditBase from "./base";
import EditChannel from "./channel";
import EditContent from "./content";

export default {
  extends: Base,
  components: { MediaImage, EditBase, EditChannel, EditContent },
  data() {
    return {
      menu: [
        { label: "基础信息", value: "", name: "base" },
        { label: "展示位置", value: "", name: "channel" },
        { label: "内容详情", value: "", name: "content" },
      ],
      model: this.value || {
        type: "news",
        status: "editing",
        title: "",
        source: "",
        subtitle: "",
        abstract: "",
        cover_url: "",
        pubdate: null,
        detail_url: "",
        content: "",
        channels: [],
        display_date: null,
        staff_id: null
      },
    };
  },
};
</script>