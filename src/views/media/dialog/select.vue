<template>
  <el-dialog class="media-select-dialog" :visible.sync="visible" @closed="closed" width="885px"
             :close-on-press-escape="false" :close-on-click-modal="false" custom-class="abs-close">

    <div class="header" slot="title">
      <div class="el-dialog__title">
        <el-select v-if="allows.length > 1" v-model="query.type" size="small" @change="doSearch">
          <el-option v-for="item in allows" :label="item.val" :value="item.key"></el-option>
        </el-select>
        <template v-else>{{ allows[0].val }}</template>
      </div>
      <el-input placeholder="检索关键字" size="mini" v-model="search.kw">
        <i slot="suffix" class="el-input__icon el-icon-search" @click="doSearch"></i>
      </el-input>
    </div>

    <div class="media-card-list">
      <div class="media-card" v-for="media in list" @click="toggleSelect(media)">
        <div class="media-item" :class="{active: selected.indexOf(media.id) > -1}">
          <i class="cover" :style="`background-image: url(${media.cover_url});`">
            <i class="top-left-corner">
              <svg aria-hidden="true" class="icon">
                <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#media-top-left"></use>
              </svg>
              {{ fType(media.type) }}
            </i>
          </i>
          <div class="title">{{ media.title }}</div>
        </div>
      </div>
    </div>

    <template slot="footer">
      <el-pagination
          @current-change="loadPage"
          :current-page="page"
          :page-size="query.limit"
          :total="total"
          layout="prev, pager, next">
      </el-pagination>
      <el-button @click="visible = false" size="mini">取 消</el-button>
      <el-button size="mini" type="primary" @click="handleOK" :disabled="checked.length == 0">确 定</el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss">
.media-select-dialog {
  .el-dialog__body {
    background: #f7f8fa;
    height: 500px;
  }

  .media-card {
    width: 266px;
    float: left;
    display: inline-block;

    &:nth-child(3n), &:nth-child(3n-1) {
      margin-left: 15px;
    }

    &:nth-of-type(n + 4) {
      margin-top: 15px;
    }

    .title {
      height: 38px;
    }
  }
}
</style>

<script>
export default {
  props: {
    limit: {
      type: Number,
      default: 1
    },
    allow: {
      type: String,
      default: 'all'
    },
    visible: {
      default: false
    }
  },
  data() {
    let allow = this.allow.split(',');
    let type = allow[0];
    let allows = [
      {key: 'news', val: '图文资源', short: '图文'},
      {key: 'audio', val: '音频资源', short: '音频'},
      {key: 'video', val: '视频资源', short: '视频'},
      {key: 'live', val: '直播资源', short: '直播'},
      {key: 'pano', val: '全景漫游', short: '全景'},
      {key: 'u3d', val: '虚拟展厅', short: '展厅'}
    ];
    let types = {};

    for (let i = allows.length - 1; i >= 0; i--) {
      types[allows[i].key] = allows[i].short;

      if (type != 'all' && allow.indexOf(allows[i].key) == -1) {
        allows.splice(i, 1);
      }
    }

    if (type == 'all') {
      allows.splice(0, 0, {key: '', val: '全部资源'});
    }

    return {
      loading: true,
      selected: [],
      checked: [],
      list: [],
      query: {kw: '', type: type == 'all' ? 'news' : type, offset: 0, limit: 21, extend: 1},
      search: {kw: ''},
      page: 1,
      total: 0,
      types: types,
      allows: allows
    }
  },
  computed: {
    audio() {
      let el = document.createElement('audio');
      el.crossOrigin = 'anonymous';
      el.onerror = () => this.played = 0;
      el.onpause = () => this.played = 0;
      el.onplay = () => this.played = 1;
      return el;
    }
  },
  created() {
    this.refresh();
  },
  methods: {
    closed() {
      this.$emit('closed');
    },
    refresh() {
      this.search.kw = '';
      this.doSearch();
    },
    doSearch() {
      Object.assign(this.query, this.search);
      this.loadPage(1);
    },
    loadPage(page) {
      this.loading = true;
      let query = Object.assign(this.query, {offset: (page - 1) * 10});
      this.$api.media.list(query).then(res => {
        this.page = page;
        this.total = res.count;
        this.list = res.rows;

        let {playing} = this;
        if (playing) {
          let exists = res.rows.some(data => {
            return data.id == playing;
          });
          if (!exists) this.playing = 0;
        }
      }).finally(() => {
        this.loading = false;
      });
    },
    toggleSelect(item) {
      let {selected, checked} = this, i = selected.indexOf(item.id);

      if (i != -1 || selected.length >= this.limit) {
        selected.splice(i, 1);
        checked.splice(i, 1);
      }

      if (i == -1) {
        selected.push(item.id);
        checked.push(item);
      }
    },
    handleOK() {
      this.$emit('resolve', this.checked);
      this.visible = false;
    },
    fType(v) {
      return this.types[v] || '未知'
    }
  }
}
</script>