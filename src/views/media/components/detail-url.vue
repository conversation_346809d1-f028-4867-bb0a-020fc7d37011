<template>
  <el-input v-model="input" @change="onChange" :placeholder="placeholder" clearable :disabled="checked">
    <el-checkbox slot="suffix" v-model="checked" @change="onCheck">禁止跳转&nbsp;</el-checkbox>
  </el-input>
</template>

<script>
export default {
  props: ['value'],
  data() {
    return {
      input: '',
      checked: false,
      placeholder: '默认无需填写，必须以' + location.protocol + '//开头的合法URL'
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(v) {
        this.input = v == '-' ? '' : v;
        this.checked = v == '-';
      }
    }
  },
  methods: {
    onChange(v) {
      this.$emit('input', v);
    },
    onCheck(v) {
      this.onChange(v ? '-' : '');
    }
  }
}
</script>