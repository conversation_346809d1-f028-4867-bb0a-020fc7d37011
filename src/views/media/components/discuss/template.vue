<template>
  <el-dialog class="panorama-reply-dialog" :visible.sync="visible" :show-close="!!reply_id" width="600px"
             @closed="closed">
    <template slot="title">
      <span class="el-dialog__title">{{ reply_id ? '查看回复' : '说一说' }}</span>
      <el-switch v-if="!reply_id"
                 v-model="enabled"
                 :active-value="1"
                 :inactive-value="0"
                 @change="toggleEnable"
                 title="评论开关"
                 style="float: right"></el-switch>
    </template>
    <el-table ref="table" highlight-current-row :data="list" :show-header="false" size="small" v-if="list.length > 0">
      <el-table-column width="80">
        <el-image slot-scope="scope" class="img-box" :src="scope.row.headimg" fit="cover"></el-image>
      </el-table-column>
      <el-table-column>
        <template slot-scope="scope">
          <div class="head">
            <div class="name">{{ scope.row.nickname }}</div>
            <div class="praise">
              <svg aria-hidden="true" class="icon">
                <use xlink:href="#zan"></use>
              </svg>
              <span>{{ scope.row.praise_num }}</span>
            </div>
          </div>
          <div class="body" v-html="scope.row.content"></div>
          <div class="foot">
            <div class="dateTime">{{ fCreated(scope.row.created) }}</div>
            <div class="reply-num" v-if="scope.row.reply_num > 0" @click="onReply(scope.row.id)">
              {{ scope.row.reply_num + '回复' }}
            </div>
            <div style="flex:1"></div>
            <div class="delete" @click="del(scope.row.id)">删除</div>
          </div>
        </template>
      </el-table-column>

    </el-table>
    <div v-else class="none">没有评论</div>
    <template slot="footer">
      <el-pagination
          @current-change="loadPage"
          :page-size="query.limit"
          layout="prev, pager, next"
          :total="total">
      </el-pagination>
      <el-button size="mini" type="primary" @click="visible=false">确 定</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      list: [],
      item_id: 0,
      reply_id: 0,
      visible: false,
      loading: true,
      enabled: 0,
      page: 0,
      total: 0
    }
  },
  computed: {
    query() {
      return {limit: 30, item_id: this.item_id, reply_id: this.reply_id}
    }
  },
  created() {
    this.loadPage(1);
  },
  mounted() {
    this.visible = true;
  },
  methods: {
    closed() {
      this.$destroy();
      this.$el.remove();
    },
    loadPage(page) {
      this.loading = true;
      let query = Object.assign(this.query, {offset: (page - 1) * 30});
      this.$api.get('media.discuss.query', query).then(res => {
        this.total = res.reply_num;
        this.list = res.list;
        this.enabled = res.allow_reply;
        this.page = page;
      }).finally(() => {
        this.loading = false;
      });
    },
    fCreated(v) {
      return this.$utils.Date(v).format();
    },
    del(id) {
      this.$confirm('操作不可恢复，确定删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.post('media.discuss.delete', {item_id: this.item_id, id: id}).then(_ => {
          this.loadPage(this.page);
        });
      });
    },
    toggleEnable(v) {
      this.$api.post('media.discuss.enabled', {item_id: this.item_id, enabled: v}).then(_ => {
        this.enabled = v;
      });
    }
  }
}
</script>

<style lang="scss">
.panorama-reply-dialog {
  .el-dialog__body {
    height: 385px;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 10px;
  }

  .el-dialog__footer {
    display: flex;
    text-align: left;
  }

  .el-pagination {
    flex: 1;
  }

  .el-table .cell {
    line-height: 1.6;
  }

  .img-box {
    width: 60px;
    height: 60px;
    overflow: hidden;
    border-radius: 50%;
    background: #f0f0f0;
  }

  .head {
    display: flex;
    justify-content: space-between;

    .praise {
      svg {
        margin-right: 4px;
      }
    }
  }

  .body {
    white-space: normal;
    font-size: 14px;
  }

  .foot {
    display: flex;
    align-items: center;

    .delete {
      color: #409eff;
      cursor: pointer;
    }
  }

  .none {
    text-align: center;
    padding-top: 160px;
    color: #999;
  }

  .name {
    color: #409eff;
  }

  .reply-num {
    border-radius: 8px;
    background: #f0f0f0;
    font-size: 12px;
    padding: 0 8px;
    line-height: 1.6;
    margin-left: 10px;
    cursor: pointer;
  }
}
</style>
