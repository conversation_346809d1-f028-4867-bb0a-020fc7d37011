<template>
  <div class="media-cover">
    <el-image v-if="value" :src="value" fit="cover"></el-image>
    <div class="is-empty" v-else>暂无封面</div>
    <div class="action">
      <div class="is-del" title="移除封面" @click="setUrl('')">
        <i class="el-icon-delete"></i>
      </div>
      <div class="is-upload" title="上传封面" @click.stop="onClick(model)">
        <i class="el-icon-picture-outline"></i>
      </div>
    </div>
  </div>
</template>

<script>
import SelectImage from '@/views/media/image/select';

export default {
  props: ['value'],
  methods: {
    setUrl(v) {
      this.value = v;
      this.$emit('input', v);
    },
    onClick() {
      SelectImage(1).then(list => this.setUrl(list[0].url));
    }
  }
}
</script>