<template>
  <el-date-picker type="datetime" placeholder="立即发布" v-model="model" @change="changed"></el-date-picker>
</template>

<script>
  export default {
    props: ['value'],
    data() {
      return {
        model: this.value ? this.value * 1000 : null
      }
    },
    watch: {
      value(v) {
        this.model = v ? v * 1000 : null;
      }
    },
    methods: {
      changed(v) {
        this.$emit('input', v ? v.valueOf() / 1000 : null);
      }
    }
  }
</script>
