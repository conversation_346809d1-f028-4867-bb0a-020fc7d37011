<template>
  <el-select title="资源类型" :placeholder="placeholder || '资源类型'" :size="size" v-model="value" @change="changed" clearable>
    <el-option v-for="item in $api.media.typeList" :label="item.label" :value="item.value"></el-option>
  </el-select>
</template>

<script>
  export default {
    props: ['value', 'placeholder', 'size'],
    methods: {
      changed(v) {
        this.$emit('input', v);
        this.$emit('change', v);
      }
    }
  }
</script>
