<template>
  <div class="el-media-select">
    <div style="display: flex">
      <media-type style="width:100px" size="small" v-model="type" @change="onTypeChange"></media-type>
      <el-select
          style="flex:1;margin-left:20px"
          filterable
          clearable
          remote
          reserve-keyword
          placeholder="请输入关键词"
          size="small"
          :remote-method="remoteMethod"
          :loading="loading"
          @change="onSelected"
          v-model="value">
        <el-option
            v-for="item in list"
            :key="item.id"
            :label="item.title"
            :value="item.id">
          <span style="float: left">{{ item.title }}</span>
          <span style="float: right; color: #8492a6; font-size: 13px">{{ $api.media.type[item.type] }}</span>
        </el-option>
      </el-select>
    </div>
    <slot></slot>
  </div>
</template>

<script>
import MediaType from './type';

export default {
  components: {MediaType},
  props: ['value'],
  data() {
    return {
      type: '',
      loading: false,
      list: [],
      media: null,
      kw: '',
      page: 1,
      noMore: false
    }
  },
  mounted() {
    this.load(1);
  },
  methods: {
    onTypeChange(v) {
      this.onSelected(null);
    },
    remoteMethod(q) {
      if (q != this.kw) {
        this.kw = q;
        this.load(1);
      }
    },
    load(page) {
      this.loading = true;
      let size = 20;
      let offset = (page - 1) * size;
      let query = {type: this.type, offset: offset, limit: size};

      if (this.kw) {
        query.kw = this.kw;
      } else {
        query.id = this.value;
      }

      this.$api.media.search(query).then(list => {
        this.list = list;
        this.page = page;
        this.noMore = list.length < size;
        this.onSelected(this.value);
      }).finally(() => {
        this.loading = false;
      });
    },
    onSelected(v) {
      let media;

      v && this.list.some(item => {
        media = item;
        return item.id == v;
      });

      if (media) {
        this.type = media.type;
        //if (!media.alias) media.alias = this.$utils.encodeId(media.id, 'media.' + media.type);
      }

      this.media = media;
      this.$emit('input', v);
      this.$emit('change', media);
    }
  }
}
</script>