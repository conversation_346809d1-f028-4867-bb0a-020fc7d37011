<template>
  <div class="play-address-multiple">
    <label class="el-checkbox" style="margin: 0px 0px 0px 10px;" v-for="item in list" @click="edit(item)">
      <span class="el-checkbox__input" :class="{'is-checked': item.checked}">
        <span class="el-checkbox__inner"></span>
      </span>
      <span class="el-checkbox__label">{{item.text}}</span>
    </label>
  </div>
</template>

<script>
  import InputDialog from './dialog';

  export default {
    props: ['value', 'disabled'],
    data() {
      return {
        list: [{
          quality: 1,
          text: '标清',
          resolution: '960x540',
          checked: false
        }, {
          quality: 2,
          text: '高清',
          resolution: '1280x720',
          checked: false
        }, {
          quality: 3,
          text: '超清',
          resolution: '1920x1080',
          checked: false
        }, {
          quality: 4,
          text: '4K',
          resolution: '3840x2160',
          checked: false
        }]
      }
    },
    computed: {
      Input() {
        return Vue.extend(InputDialog);
      }
    },
    /*
    watch: {
      value(v) {
        this.init();
      }
    },
     */
    created() {
      this.init();
    },
    destroyed() {
      this.input && this.input.$destroy();
    },
    methods: {
      init() {
        this.value.forEach(item => {
          Object.assign(this.list[item.quality - 1], {mp4: '', m3u8: '', flv: '', rtmp: '', checked: true}, item);
        });
      },
      edit(item) {
        let {input} = this;
        if (!input) {
          this.input = input = new this.Input({
            propsData: {value: item, disabled: this.disabled}
          }).$mount();
        } else {
          input.value = item;
          input.disabled = this.disabled;
        }

        input.$once('closed', () => {
          let list = this.value, index = -1, exists = list.some((data, i) => {
            if (data.quality == item.quality) {
              return index = i, true;
            } else if (data.quality < item.quality) {
              index = i + 1;
            }
          });

          if (item.checked) {
            let data = Object.assign(exists ? list[index] : {}, item);
            if (!item.mp4) delete data.mp4;
            if (!item.m3u8) delete data.m3u8;
            if (!item.flv) delete data.flv;
            if (!item.rtmp) delete data.rtmp;
            exists || list.splice(index, 0, data);
          } else if (exists) {
            list.splice(index, 1);
          }
        });

        document.body.appendChild(input.$el);
        input.visible = true;
      }
    }
  }
</script>