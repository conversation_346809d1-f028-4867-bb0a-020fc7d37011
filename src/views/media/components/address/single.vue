<template>
  <div class="play-address-single">
    <el-popover placement="top" width="300" v-for="item in list" @hide="valid(item)">
      <div class="el-input el-input--mini" :class="disabled ? 'is-disabled' : 'el-input--suffix'" style="width: 100%;">
        <input v-model="item.value" :disabled="disabled" type="text" autocomplete="off" class="el-input__inner">
        <span class="el-input__suffix" v-if="!disabled" @click="clear(item)">
          <span class="el-input__suffix-inner">
          <i class="el-input__icon el-icon-circle-close el-input__clear"></i>
          </span>
        </span>
      </div>
      <label slot="reference" class="el-checkbox" style="margin: 0px 0px 0px 10px;">
        <span class="el-checkbox__input" :class="{'is-checked': item.checked}">
          <span class="el-checkbox__inner"></span>
        </span>
        <span class="el-checkbox__label">{{item.text}}</span>
      </label>
    </el-popover>
  </div>
</template>

<script>
  export default {
    props: ['value', 'disabled'],
    data() {
      return {
        list: []
      }
    },
    watch: {
      value(v) {
        this.init();
      }
    },
    created() {
      this.init();
    },
    methods: {
      init() {
        let data = this.value;
        this.list = [
          {text: 'rtmp', checked: !!data.rtmp, value: data.rtmp},
          {text: 'flv', checked: !!data.flv, value: data.flv},
          {text: 'm3u8', checked: !!data.m3u8, value: data.m3u8},
          {text: 'mp4', checked: !!data.mp4, value: data.mp4}
        ];
      },
      clear(item) {
        item.value = '';
        item.checked = false;
      },
      valid(item) {
        if (item.text == 'rtmp') {
          item.checked = validator.isURL(item.value, {protocols: ['rtmp']});
        } else {
          item.checked = validator.isURL(item.value, {protocols: ['http', 'https']});
        }
        this.value[item.text] = item.checked ? item.value : ''
      }
    }
  }
</script>