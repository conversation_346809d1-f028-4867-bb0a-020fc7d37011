<template>
    <div>
      <mytable ref="table" :list="list" :api="'/v1/mini.volunteer.activity.plan.list'" :query="query" :pagination="true">
        <div class="table-toolbar" slot="toolbar">
          <div class="toolbar-header">
            <div class="toolbar-title">{{ $route.meta.title }}</div>
            <div class="toolbar-actions">
              <template>
                <el-button type="primary" size="mini" @click="add">添加</el-button>
              </template>
            </div>
          </div>
        </div>
  
        <el-table-column label="活动名称" prop="activity_name" align="center" ></el-table-column>
        <el-table-column label="活动日期" prop="activity_date" align="center" ></el-table-column>
        <el-table-column label="开始时间" prop="start_time" align="center" ></el-table-column>
        <el-table-column label="结束时间" prop="end_time" align="center" ></el-table-column>
        <el-table-column label="活动地点" prop="location" align="center" ></el-table-column>
        <el-table-column label="主办单位" prop="organizer" align="center" ></el-table-column>
        <el-table-column label="活动负责人" prop="person_in_charge" align="center" ></el-table-column>
        <el-table-column label="联系方式" prop="contact" align="center" ></el-table-column>
        <el-table-column label="服务类别" prop="service_type" align="center" >
            <template slot-scope="scope">
              <span v-if="scope.row.service_type === '0'">社区服务类</span>
              <span v-else-if="scope.row.service_type === '1'">人文关怀类</span>
              <span v-else-if="scope.row.service_type === '2'">文化建设类</span>
              <span v-else-if="scope.row.service_type === '3'">健康促进类</span>
              <span v-else-if="scope.row.service_type === '4'">应急管理类</span>
              <span v-else-if="scope.row.service_type === '5'">自治管理类</span>
            </template>
        </el-table-column>
        <el-table-column label="备注" prop="remarks" align="center" ></el-table-column>
       
        <el-table-column label="操作" align="center" width="190px">
          <template slot-scope="scope">
            <template>
              
              <el-button class="btn-action" size="mini" @click.stop="onDetail(scope.row)" title="查看">
                <svg class="icon" aria-hidden="true">
                  <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
                </svg>
              </el-button>
              <el-button class="btn-action" size="mini" @click.stop="onDel(scope.row)" title="删除">
                删除
              </el-button>
            </template>
          </template>
        </el-table-column>
      </mytable>


      <el-dialog title="志愿者活动计划" :visible.sync="visible" class="media-upload-video"
           width="600px" :before-close="beforeClose" @closed="closed" :close-on-press-escape="false"
           :close-on-click-modal="false">
        <el-form ref="forms" :model="model" :rules="rules" size="small" label-width="120px" auto-complete="off"
                style="margin-right:40px">
          <el-form-item label="活动名称" prop="activity_name">
            <el-input v-model="model.activity_name" placeholder="请输入活动名称"></el-input>
          </el-form-item>
          <el-form-item label="活动内容" prop="activity_content">
            <el-input type="textarea" :rows="4" v-model="model.activity_content" placeholder="请输入活动内容"></el-input>
          </el-form-item>
          <el-form-item label="活动日期" prop="activity_date">
            <el-date-picker v-model="model.activity_date" type="date" style="width: 100%;" placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item label="开始时间" prop="start_time">
            <el-time-picker v-model="model.start_time" placeholder="选择时间" style="width: 100%;" format="HH:mm" value-format="HH:mm"></el-time-picker>
          </el-form-item>
          <el-form-item label="结束时间" prop="end_time">
            <el-time-picker v-model="model.end_time" placeholder="选择时间" style="width: 100%;" format="HH:mm" value-format="HH:mm"></el-time-picker>
          </el-form-item>
          <el-form-item label="活动地点" prop="location">
            <el-input v-model="model.location" placeholder="请输入活动地点"></el-input>
          </el-form-item>
          <el-form-item label="主办单位" prop="organizer">
            <el-input v-model="model.organizer" placeholder="请输入主办单位"></el-input>
          </el-form-item>
          <el-form-item label="活动负责人" prop="person_in_charge">
            <el-input v-model="model.person_in_charge" placeholder="请输入活动负责人"></el-input>
          </el-form-item>
          <el-form-item label="联系方式" prop="contact">
            <el-input v-model="model.contact" placeholder="请输入联系方式"></el-input>
          </el-form-item>
          <el-form-item label="服务类别" prop="service_type">
            <el-select v-model="model.service_type" placeholder="请选择服务类别">
              <el-option label="社区服务类" value="0"></el-option>
              <el-option label="人文关怀类" value="1"></el-option>
              <el-option label="文化建设类" value="2"></el-option>
              <el-option label="健康促进类" value="3"></el-option>
              <el-option label="应急管理类" value="4"></el-option>
              <el-option label="自治管理类" value="5"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="remarks">
            <el-input type="textarea" :rows="3" v-model="model.remarks" placeholder="请输入备注"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="visible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </el-dialog>

    </div>
  </template>
    
  <script>

  export default {
    data() {
      return {
        
        child_visible:false,
        visible:false,
        list:[],
        model: {
        activity_name: '',
        activity_content: '',
        activity_date: '',
        start_time: '',
        end_time: '',
        location: '',
        organizer: '',
        person_in_charge: '',
        contact: '',
        service_type: '0',
        remarks: ''
      },
      rules: {
        activity_name: [
          { required: true, message: '请输入活动名称', trigger: 'blur' },
          { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
        ],
        activity_content: [
          { required: true, message: '请输入活动内容', trigger: 'blur' },
          { max: 500, message: '长度不能超过500个字符', trigger: 'blur' }
        ],
        activity_date: [
          { required: true, message: '请选择活动日期', trigger: 'change' }
        ],
        start_time: [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        end_time: [
          { required: true, message: '请选择结束时间', trigger: 'change' }
        ],
        location: [
          { required: true, message: '请输入活动地点', trigger: 'blur' },
          { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
        ],
        organizer: [
          { required: true, message: '请输入主办单位', trigger: 'blur' },
          { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
        ],
        person_in_charge: [
          { required: true, message: '请输入活动负责人', trigger: 'blur' },
          { max: 32, message: '长度不能超过32个字符', trigger: 'blur' }
        ],
        contact: [
          { required: true, message: '请输入联系方式', trigger: 'blur' },
          { max: 16, message: '长度不能超过16个字符', trigger: 'blur' }
        ],
        service_type: [
          { required: true, message: '请选择服务类别', trigger: 'change' }
        ],
        remarks: [
          { max: 255, message: '长度不能超过255个字符', trigger: 'blur' }
        ]
      }
      };
    },
    methods: {
      refresh() {
        this.$refs.table.refresh();
      },
      beforeClose(done) {
        this.$refs.forms.resetFields();
        done();
      },
      closed() {
        this.model = {
          activity_name: '',
          activity_content: '',
          activity_date: '',
          start_time: '',
          end_time: '',
          location: '',
          organizer: '',
          person_in_charge: '',
          contact: '',
          service_type: '0',
          remarks: ''
        };
      },
      onDel(v) {
        this.$confirm("操作不可恢复，确定删除吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            return this.$api.post('/v1/mini.volunteer.activity.plan.delete', { id: v.id });
          })
          .then(this.refresh);
      },
      async onDetail(v) {
        let res = await this.$api.get('/v1/mini.volunteer.activity.plan.query', { id: v.id });
        this.visible = true;
        this.model = res;
      },
      submitForm() {
        this.$refs.forms.validate(async (valid) => {
          if (valid) {
            try {
              // 如果没有id则是新增，有id则是修改
              if (!this.model.id) {
                // 新增时添加创建时间
                this.model.created_at = Math.floor(Date.now() / 1000);
                await this.$api.post('/v1/mini.volunteer.activity.plan.create', this.model);
                this.$message.success('添加成功');
              } else {
                await this.$api.post('/v1/mini.volunteer.activity.plan.update', this.model);
                this.$message.success('修改成功');
              }
              this.visible = false;
              this.refresh();
            } catch (error) {
              this.$message.error('操作失败：' + (error.message || '未知错误'));
            }
          } else {
            return false;
          }
        });
      },
      add() {
        this.visible = true;
        this.model = {
          activity_name: '',
          activity_content: '',
          activity_date: '',
          start_time: '',
          end_time: '',
          location: '',
          organizer: '',
          person_in_charge: '',
          contact: '',
          service_type: '0',
          remarks: ''
        };
      }
      
    },
  };
  </script>
  <style scoped lang="scss">
  .please {
    line-height: 50px;
    height: 50px;
    width: 100%;
  }
  
  .btns {
    margin-top: 50px;
    width: 100%;
    text-align: right;
  }
  
  
  /deep/ .el-dialog__body {
    text-align: center !important;
  }
  
  </style>