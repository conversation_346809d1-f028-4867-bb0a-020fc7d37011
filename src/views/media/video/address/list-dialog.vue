<template>
  <el-dialog title="播放地址" class="video-address-dialog" :visible.sync="visible" width="600px"
             :close-on-click-modal="false"
             :close-on-press-escape="false"
             :append-to-body="true"
             :show-close="!disabled"
             :before-close="beforeClose"
             @closed="$emit('closed')">

    <div class="va-list" v-show="!editing">
      <div class="va-item">
        <div class="va-header">
          <div class="va-title">原始</div>
        </div>
        <div class="va-content">
          <p>{{ original }}</p>
        </div>
      </div>
      <div class="va-item" v-for="(item, index) in list">
        <div class="va-header">
          <div class="va-title">{{ item.text }}</div>
          <div>
            <div class="va-link" @click="onDel(item)" v-if="item.checked">清除</div>
            <div class="va-link" @click="onEdit(index)">编辑</div>
          </div>
        </div>
        <div class="va-content">
          <p v-if="item.mp4">{{ item.mp4 }}</p>
          <p v-if="item.m3u8">{{ item.m3u8 }}</p>
          <p v-if="item.flv">{{ item.flv }}</p>
        </div>
      </div>
    </div>

    <el-form v-if="editing" label-width="90px" size="small" style="margin-right:40px">
      <el-form-item label="清晰度">
        <el-select v-model="model.text" allow-create filterable>
          <el-option label="标清" value="标清"></el-option>
          <el-option label="高清" value="高清"></el-option>
          <el-option label="超清" value="超清"></el-option>
          <el-option label="2K" value="2K"></el-option>
          <el-option label="4K" value="4K"></el-option>
        </el-select>
      </el-form-item>
      <template v-if="input">
        <el-form-item label=".mp4">
          <el-input v-model.trim="model.mp4" clearable maxlength="128"></el-input>
          <div class="btn-up">
            <el-button type="primary" size="mini">上传</el-button>
            <input ref="file" type="file" accept=".mp4" @change="onMp4">
          </div>
        </el-form-item>
        <el-form-item label=".m3u8">
          <el-input v-model.trim="model.m3u8" clearable maxlength="128"></el-input>
        </el-form-item>
        <el-form-item label=".flv">
          <el-input v-model.trim="model.flv" clearable maxlength="128"></el-input>
        </el-form-item>
      </template>
      <el-form-item v-else label="待上传">
        <div class="ad-mp4">
          <div class="ad-del" @click="delMp4"><i class="el-icon-delete"></i></div>
        </div>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button v-show="!editing">关 闭</el-button>
      <el-button v-show="editing" @click="editing=0" :disabled="disabled">取 消</el-button>
      <el-button type="primary" v-show="editing" @click="onSave" v-if="input" :disabled="disabled">保 存</el-button>
      <el-button type="primary" v-show="editing" @click="onUpload" v-else :disabled="disabled">{{ button }}</el-button>
    </div>
  </el-dialog>
</template>

<style lang="scss">
.video-address-dialog {
  .el-dialog__body {
    max-height: 360px;
    overflow-y: auto;
  }

  .va-header {
    padding: 10px 0;
    display: flex;
    align-items: center;
  }

  .va-title {
    font-weight: bold;
    flex: 1;
  }

  .va-link {
    color: #3a8ee6;
    cursor: pointer;
    display: inline-block;
    margin-left: 10px;
  }

  .va-content {
    padding: 6px 10px;
    background: #f0f0f0;
  }

  p {
    margin: 4px 0;
    line-height: 1;
  }

  .btn-up {
    position: absolute;
    right: 2px;
    top: 0;
    bottom: 0;
  }

  .ad-mp4 {
    height: 232px;
    background: #000;
  }

  .ad-del {
    cursor: pointer;
    position: absolute;
    right: 0;
    top: 0;
    width: 32px;
    height: 32px;
    text-align: center;
    z-index: 1;
    color: #fff;
    background: rgba(0, 0, 0, 0.4);
  }

  video {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
</style>

<script>
export default {
  props: ['itemId', 'itemType'],
  data() {
    return {
      visible: false,
      disabled: false,
      editing: false,
      index: 0,
      model: null,
      input: true,
      button: '上 传',
      original: '',
      list: [{
        text: '标清',
        quality: 1,
        mp4: '',
        m3u8: '',
        flv: '',
        checked: false
      }, {
        text: '高清',
        quality: 2,
        mp4: '',
        m3u8: '',
        flv: '',
        checked: false
      }, {
        text: '超清',
        quality: 3,
        mp4: '',
        m3u8: '',
        flv: '',
        checked: false
      }, {
        text: '2K',
        quality: 4,
        mp4: '',
        m3u8: '',
        flv: '',
        checked: false
      }, {
        text: '4K',
        quality: 5,
        mp4: '',
        m3u8: '',
        flv: '',
        checked: false
      }]
    }
  },
  computed: {
    video() {
      let el = document.createElement('video');
      el.muted = true;
      el.autoplay = true;
      el.controls = true;
      return el;
    }
  },
  created() {
    this.$api.get('media.item.getAddress', {id: this.itemId, type: this.itemType}).then(res => {
      this.original = res.original_url;
      let list = this.list;

      res.address.forEach(item => {
        Object.assign(list[item.quality - 1], item, {checked: true})
      })
    });
  },
  mounted() {
    this.visible = true;
  },
  methods: {
    beforeClose(done) {
      this.disabled || done();
    },
    onEdit(i) {
      this.index = i;
      this.model = Object.assign({}, this.list[i]);
      this.editing = true;
    },
    onDel(item) {
      this.$confirm('确定要清除【' + item.text + '】播放地址吗？').then(_ => {
        item.checked = false;
        this.submit();
      });
    },
    submit() {
      this.disabled = true;
      let address = [];

      this.list.forEach(item => {
        item.checked && address.push({
          text: item.text,
          quality: item.quality,
          mp4: item.mp4 || undefined,
          m3u8: item.m3u8 || undefined,
          flv: item.flv || undefined
        });
      });

      this.$api.post('media.item.setAddress', {id: this.itemId, type: this.itemType, address: address}).finally(_ => {
        this.disabled = false;
      });
    },
    onSave() {
      this.disabled = true;
      let {model, list, index} = this;
      model = Object.assign(list[index], model, {checked: true});

      if (!validator.isURL(model.mp4) && !validator.isURL(model.m3u8) && !validator.isURL(model.flv)) {
        return this.$message.warning('至少填写一个播放地址');
      }

      this.submit();
    },
    onMp4(e) {
      this.input = false;
      let file = e.target.files[0];
      this.file = file;
      this.button = '上 传';

      this.$nextTick(() => {
        let video = this.video;
        this.$el.querySelector('.ad-mp4').appendChild(video);
        video.src = URL.createObjectURL(file);
        video.play();
      });

      e.target.value = '';
    },
    delMp4() {
      this.input = true;
      let video = this.video;
      video.remove();
      URL.revokeObjectURL(video.src);
      video.src = '';
    },
    onUpload() {
      if (this.disabled) return;

      this.disabled = true;
      let file = this.file,
          size = file.size,
          percent = 0;

      this.$api.media.upload.request('video', this.file, (p) => {
        percent = (p / size * 100).toFixed(2);
        percent = parseFloat(percent);
        percent = percent > 100 ? 100 : percent;
        this.button = '已上传(' + percent + '%)';
      }).then(url => {
        this.disabled = false;
        this.model.mp4 = url;
        this.delMp4();
        return this.onSave();
      });
    }
  }
}
</script>