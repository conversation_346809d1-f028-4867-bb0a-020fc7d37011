<template>
  <el-dialog class="media-select-dialog" :visible.sync="visible" :before-close="beforeClose" @closed="closed" width="660px"
             :close-on-press-escape="false" :close-on-click-modal="false" custom-class="abs-close">

    <div class="header" slot="title">
      <div class="el-dialog__title">视频素材库</div>
      <el-input placeholder="检索关键字" size="mini" v-model="search.kw">
        <i slot="suffix" class="el-input__icon el-icon-search" @click="doSearch"></i>
      </el-input>
    </div>

    <div class="media-select-table" v-if="list.length > 0" v-loading="loading">
      <div class="tr" v-for="item in list">
        <div class="td">
          <label class="el-checkbox" @click="toggleSelect(item)">
            <span class="el-checkbox__input" :class="{'is-checked': selected.indexOf(item.id) > -1}">
              <span class="el-checkbox__inner"></span>
            </span>
          </label>
        </div>
        <div class="td" @click="togglePlay(item)">
          <svg class="icon">
            <use :xlink:href="`#${playing == item.id && played ? 'pause' : 'play'}`"></use>
          </svg>
        </div>
        <div class="td ellipsis title">{{ item.title }}</div>
        <div class="td">{{ item.lower_right_corner }}</div>
      </div>
    </div>
    <div v-else class="none">未匹配到视频素材</div>

    <template slot="footer">
      <el-pagination
          @current-change="loadPage"
          :current-page="page"
          :page-size="query.size"
          :total="total"
          layout="prev, pager, next">
      </el-pagination>
      <el-button v-if="upload" @click="showUpload" size="mini">上 传</el-button>
      <el-button v-else @click="cancel" size="mini">取 消</el-button>
      <el-button size="mini" type="primary" @click="handleOK" :disabled="checked.length == 0">确 定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import Upload from '../upload';

export default {
  data() {
    return {
      visible: false,
      loading: true,
      limit: 1,
      upload: 1,
      uploading: 0,
      selected: [],
      checked: [],
      playing: 0,
      played: 0,
      list: [],
      query: {kw: '', type: 'video', offset: 0, size: 15, status: 'online'},
      search: {kw: ''},
      page: 1,
      total: 0
    }
  },
  computed: {
    audio() {
      let el = document.createElement('audio');
      el.crossOrigin = 'anonymous';
      el.onerror = () => this.played = 0;
      el.onpause = () => this.played = 0;
      el.onplay = () => this.played = 1;
      return el;
    }
  },
  created() {
    this.refresh();
  },
  beforeDestroy() {
    this.pause();
  },
  methods: {
    beforeClose(done) {
      this.$emit('reject');
      done();
      this.pause();
    },
    closed() {
      if (this.uploading) {
        Upload().then(this.refresh).finally(() => {
          this.uploading = false;
          this.visible = true;
        });
      } else {
        requestAnimationFrame(() => {
          this.$destroy();
          this.$el.remove();
        });
      }
    },
    cancel() {
      this.$emit('reject');
      this.visible = false;
    },
    refresh() {
      this.search.kw = '';
      this.doSearch();
    },
    doSearch() {
      Object.assign(this.query, this.search);
      this.loadPage(1);
    },
    loadPage(page) {
      this.loading = true;
      let query = Object.assign(this.query, {
        offset: (page - 1) * this.query.size,
        creator: this.$store.state.admin.id
      });

      this.$api.media.list(query).then(res => {
        this.page = page;
        this.total = res.count;
        this.list = res.rows;

        let {playing} = this;
        if (playing) {
          let exists = res.rows.some(data => {
            return data.id == playing;
          });
          if (!exists) this.playing = 0;
        }
      }).finally(() => {
        this.loading = false;
      });
    },
    pause() {
      this.audio.pause();
    },
    togglePlay(item) {
      if (this.playing == item.id) {
        return this.played ? this.audio.pause() : this.audio.play();
      }

      this.playing = item.id;
      this.audio.src = item.file_url;
      this.audio.play();
    },
    toggleSelect(item) {
      let {selected, checked} = this, i = selected.indexOf(item.id);

      if (i != -1 || selected.length >= this.limit) {
        selected.splice(i, 1);
        checked.splice(i, 1);
      }

      if (i == -1) {
        selected.push(item.id);
        checked.push(item);
      }
    },
    handleOK() {
      this.$emit('resolve', this.checked);
      this.visible = false;
    },
    showUpload() {
      this.uploading = true;
      this.visible = false;
    }
  }
}
</script>