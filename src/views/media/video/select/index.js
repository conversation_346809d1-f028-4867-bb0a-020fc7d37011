import Dialog from './dialog.vue';

let View, view;

export default function (limit = 1) {
  return new Promise(function (resolve, reject) {
    if (!View) View = Vue.extend(Dialog);

    let el = document.createElement('div');
    document.body.appendChild(el);

    view = new View({el: el, data: {limit: limit}});
    view.$once('resolve', resolve);
    view.$once('reject', reject);
    view.visible = true;
  });
}