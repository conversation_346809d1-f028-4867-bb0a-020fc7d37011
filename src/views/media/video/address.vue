<template>
  <el-dialog
      class="video-modal"
      title="播放地址"
      :visible.sync="isShow"
      v-on:closed="close"
      width="600px">

    <el-form v-if="isShow" ref="form" label-width="100px" auto-complete="off" :model="model" :rules="rules" style="margin-right:40px;" size="small">
      <el-form-item label="文本" prop="text">
        <el-select v-model="model.text" style="display:block">
          <el-option label="极速" value="极速"></el-option>
          <el-option label="流畅" value="流畅"></el-option>
          <el-option label="标清" value="标清"></el-option>
          <el-option label="高清" value="高清"></el-option>
          <el-option label="超清" value="超清"></el-option>
          <el-option label="蓝光" value="蓝光"></el-option>
          <el-option label="2K" value="2K"></el-option>
          <el-option label="4K" value="4K"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="mp4" prop="mp4">
        <el-input v-model="model.mp4" placeholder="请输入播放地址"></el-input>
      </el-form-item>
      <el-form-item label="m3u8" prop="m3u8">
        <el-input v-model="model.m3u8" placeholder="请输入播放地址"></el-input>
      </el-form-item>
      <el-form-item label="flv" prop="flv">
        <el-input v-model="model.flv" placeholder="请输入播放地址"></el-input>
      </el-form-item>
      <el-form-item label="rtmp" prop="rtmp">
        <el-input v-model="model.rtmp" placeholder="请输入播放地址"></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="submit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  function getModel(v) {
    return Object.assign({
      resolution: '960x540',
      text: '标清',
      kbps: '900',
      quality: 1,
      mp4: '',
      flv: '',
      m3u8: '',
      rtmp: '',
      label: '960 x 540',
      checked: false
    }, v || {});
  }

  export default {
    props: ['value', 'visible'],
    data(){
      let validateAddress = (rule, value, callback) => {
        let model = this.model;
        callback((model.mp4 || model.flv || model.m3u8 || model.rtmp) ? undefined : '至少输入一种地址');
      };

      let validateRtmp = (rule, value, callback) => {
        if(value && !/^rtmp:\/\/(\w+\.){1,3}\w+\/\w+.*/.test(value)){
          callback('地址格式错误');
        }

        validateAddress(rule, value, callback);
      };

      return {
        model: null,
        isShow: false,
        rules: {
          resolution: { required: true, trigger: 'blur', message: '请选择或输入' },
          mp4: { trigger: 'blur', validator: validateAddress},
          flv: { trigger: 'blur', validator: validateAddress},
          m3u8: { trigger: 'blur', validator: validateAddress},
          rtmp: { trigger: 'blur', validator: validateRtmp}
        }
      }
    },
    watch: {
      visible(v){
        if(v){
          this.model = getModel(this.value);
        }else{
          let model = this.value;
          this.$nextTick(function(){
            let has = model.mp4 || model.m3u8 || model.rtmp || model.flv;
            if(!has){
              model.checked = false;
            }
          });
        }
        this.isShow = v;
      }
    },
    methods: {
      close(){
        this.$emit('update:visible', false);
      },
      submit(){
        this.$refs.form.validate(valid => {
          if(!valid){
            return;
          }

          let model = this.model;
          model.checked = true;

          this.$emit('input', model);
          this.$emit('change', model);
          this.close();
        });
      },
      remove(){
        this.value.checked = false;
        this.close();
      }
    },
    mounted(){
      document.body.appendChild(this.$el);
    }
  }
</script>