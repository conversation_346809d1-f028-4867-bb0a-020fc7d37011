<template>
  <el-dialog
      :visible.sync="visible"
      class="player-code-dialog"
      width="600px"
      :show-close="false"
      :before-close="close">

    <div class="player-code-title" slot="title">
      <div class="item">通用代码</div>
      <el-switch class="is-autoplay" v-model="autoplay" title="自动播放" :active-value="1" :inactive-value="0"></el-switch>
    </div>

    <div style="background:#1B2426;padding:10px 20px;margin-bottom:20px;font-size:12px;color:#fff">
      使用说明：请将下面代码粘贴到要显示播放器的元素中；如转码未完成或失败将只能播放原始画面
    </div>

    <div id="videoPlayerIframeCode" style="background:#1B2426;padding:10px 20px;">
      <p><code>&lt;iframe</code></p>
      <p style="text-indent: 2em"><code>src="{{CDN_URL}}/iframe.html?id={{media.id}}&type={{media.type}}&autoplay={{autoplay}}"</code></p>
      <p style="text-indent: 2em"><code>frameborder="0"</code></p>
      <p style="text-indent: 2em"><code>allowfullscreen="true"&gt;</code></p>
      <p><code>&lt;/iframe&gt;</code></p>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button id="copyCode" type="primary">复 制</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    data(){
      return {
        visible: true,
        active: 1,
        media: null,
        autoplay: 1
      }
    },
    mounted(){
      let $this = this;
      this.$require('clipboard').then(function(){
        let clipboard = new ClipboardJS('#copyCode', {
          text: function(trigger) {
            return document.getElementById('videoPlayerIframeCode').innerText;
          }
        });

        clipboard.on('success', function(e) {
          e.clearSelection();
          $this.$notify.success('复制成功');
          $this.close();
        });

        clipboard.on('error', function(e) {
          $this.$notify.error('复制失败');
        });

      });
    },
    methods: {
      close(){
        this.$destroy();
        this.$el.remove();
      }
    }
  }
</script>
