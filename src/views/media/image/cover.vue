<template>
  <div class="upload-cover-image">
    <div class="is-empty" v-if="isEmpty">
      <slot name="empty">{{ emptyText }}</slot>
    </div>
    <el-image v-else :src="value" :fit="fit"></el-image>
    <div class="action">
      <div class="is-del" title="移除" v-if="!isEmpty" @click.stop.prevent="onClear">
        <i class="el-icon-delete"></i>
      </div>
      <div class="is-upload" title="上传" @click.stop="onClick">
        <i class="el-icon-picture-outline"></i>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.upload-cover-image {
  width: 120px;
  height: 120px;

  .el-image, .is-empty {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }

  .is-empty {
    background: #f5f7fa;
    color: #C0C4CC;
    display: flex;
    text-align: center;
    align-items: center;
    justify-content: center;
  }

  .action {
    display: none;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
  }

  &:hover .action {
    display: block;
  }

  .is-del, .is-upload {
    cursor: pointer;
    position: absolute;
    width: 32px;
    height: 32px;
    text-align: center;
    line-height: 32px;
    color: #fff;
  }

  .is-del {
    right: 0;
    top: 0;
  }

  .is-upload {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>

<script>
import SelectImage from '@/views/media/image/select';

export default {
  props: {
    value: {
      type: String,
      default: ''
    },
    fit: {
      type: String,
      default: 'cover'
    },
    emptyText: {
      type: String,
      default: '请上传'
    },
  },
  computed: {
    isEmpty() {
      return !this.value;
    }
  },
  methods: {
    setUrl(v) {
      this.value = v;
      this.$emit('input', v);
      this.$emit('change', v);
    },
    onClick() {
      SelectImage(1).then(list => this.setUrl(list[0].url));
    },
    onClear() {
      this.setUrl('')
    }
  }
}
</script>