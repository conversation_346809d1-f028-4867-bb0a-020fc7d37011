import View from './dialog.vue';

let MyView;

export default function (limit, params) {
  return new Promise(function (resolve, reject) {
    if (!MyView) {
      MyView = Vue.extend(View);
    }

    params = params || {};

    let cnf = {
      size: params.size || 300,
      limit: limit,
      accept: params.accept ? Array.isArray(params.accept) ? params.accept.join(',') : params.accept : 'image/jpeg,image/png,image/bmp',
      compress: params.compress === undefined || params.compress ? 1 : 0,
      network: params.network === undefined || params.network ? 1 : 0
    };

    cnf.placeholder = params.placeholder || '建议尺寸不超过800像素，文件大小不超过' + cnf.size + 'KB'

    let instance = new MyView({
      el: document.createElement('div'),
      data: {cnf: cnf}
    });

    instance.$on('cancel', function () {
      instance.$destroy();
      instance.$el.remove();
    });

    instance.$on('success', function (list) {
      resolve(list);
    });

    document.body.appendChild(instance.$el);
  });
}