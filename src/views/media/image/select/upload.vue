<template>
  <div class="upload-body" :class="{ 'is-upping': upping }">
    <div
      class="upload-content clearfix"
      :class="{ 'only-one': list.length == 0 }"
    >
      <!-- 待上传列表 -->
      <div tabindex="0" v-for="(item, index) in list" class="img-item">
        <img :src="item.url" @load="imgLoaded(index)" alt />
        <div class="message">{{ item.message }}</div>
        <div class="image-meta">{{ item.width }}x{{ item.height }}</div>
        <div class="upimg-tip">
          <a href="javascript:"
            >更改<input
              type="file"
              class="file"
              @change="fileChanged(index)"
              :accept="cnf.accept"
          /></a>
          <a href="javascript:" @click="removeFile(index)">移除</a>
        </div>
        <div class="message">{{ item.message }}</div>
        <div class="uploading" :style="`width:${item.progress}%`"></div>
      </div>
      <!-- 选择文件 -->
      <div tabindex="0" class="img-item">
        <img :src="`${CDN_URL}/sys/upimg.png`" />
        <input
          type="file"
          class="file"
          :disabled="upping"
          v-show="loaded"
          v-on:change="fileChanged(-1)"
          :accept="cnf.accept"
          multiple="multiple"
        />
      </div>
      <div class="size-tip">{{ cnf.placeholder }}</div>
    </div>

    <div class="footer-action">
      <slot name="cancel"></slot>
      <el-button
        type="primary"
        @click="startUpload"
        :disabled="upping || list.length == 0"
        >{{ btnText }}</el-button
      >
    </div>
  </div>
</template>

<script>
export default {
  inject: ["$dialog"],
  data() {
    return {
      list: [],
      upping: false,
      loaded: false,
      btnText: "上传",
      maxwh: 1920,
      status: {
        1: "待上传",
        2: "上传中",
        3: "已上传",
        4: "失　败",
      },
    };
  },
  computed: {
    cnf() {
      return this.$dialog.cnf;
    },
  },
  created() {
    this.$require("lrz").then(() => {
      this.loaded = true;
    });
  },
  methods: {
    calcWH(w, h, m) {
      if (w > m) {
        h = Math.ceil((m / w) * h);
        w = m;
      } else if (h > m) {
        w = Math.ceil((m / h) * w);
        h = m;
      } else {
        return [w, h];
      }

      return this.calcWH(w, h, m);
    },
    imgLoaded(index) {
      let img = this.list[index];
      if (img.width > 0) {
        return;
      }

      let ele = event.target,
        width = ele.naturalWidth,
        height = ele.naturalHeight;

      // if (width > 5000 || height > 5000) {
      //   this.list.splice(index, 1);
      //   this.$message.error("原始文件不能超过5000x5000像素");
      //   return;
      // }

      URL.revokeObjectURL(img.url);

      // 压缩
      // [img.width, img.height] = this.yasuotupian(
      //   img.file,
      //   width,
      //   height,
      //   (err, file) => {
      //     if (err) {
      //       this.list.splice(index, 1);

      //       return this.$notify.error({
      //         title: "错误",
      //         dangerouslyUseHTMLString: true,
      //         message:
      //           "<div>" +
      //           err +
      //           '</div><div><a href="https://tinypng.com/" target="_blank">点击压缩图片</a></div>',
      //       });
      //     }

      //     img.url = URL.createObjectURL(file);
      //     img.file = file;
      //     img.size = file.size;
      //   }
      // );
    },
    yasuotupian(file, width, height, callback, prev) {
      let { size, compress } = this.cnf,
        maxSize = size * 1024,
        quality = 0.7;

      if (
        (!compress && file.size > maxSize) ||
        (prev && file.size >= prev.size)
      ) {
        return callback(`图片 ${file.name} 超出 ${size}KB 限制`), [];
      } else {
        console.log("不超出");
      }

      let isYaSuo;
      if (width > this.maxwh || height > this.maxwh) {
        isYaSuo = 1;
        quality = 0.6;
        [width, height] = this.calcWH(width, height, this.maxwh);
      } else {
        isYaSuo = file.size > maxSize;
      }

      if (isYaSuo) {
        lrz(file, { quality: quality, width: width, height: height })
          .then((rst) => {
            if (!rst.file.name) rst.file.name = file.name;
            this.yasuotupian(rst.file, width, height, callback, file);
          })
          .catch((e) => {
            callback(e.message || e);
          });
      } else {
        callback(null, file);
      }

      return [width, height];
    },
    fileChanged(index) {
      this.btnText = "上传";

      let { list } = this;
      let ele = event.target || event.srcElement;

      for (let i = 0, len = ele.files.length; i < len; i++) {
        let file = ele.files[i];

        let media = {
          url: URL.createObjectURL(file),
          size: file.size,
          ext: file.name.split(".")[1],
          name: file.name.split(".")[0],
          width: 0,
          height: 0,
          file: file,
          status: 1,
          message: "待上传",
        };

        if (index === -1) {
          list.push(media);
          console.log(list);
        } else {
          Object.assign(list[index], media);
        }
      }

      ele.value = "";
    },
    removeFile(index) {
      this.list.splice(index, 1);
      this.files.splice(index, 1);
    },
    addMedia(media) {
      if (media.status == 3) return media;

      media.status = 2;

      let file =
        media.file instanceof File
          ? media.file
          : new File([media.file], media.file.name, { type: media.file.type });

      return this.$api.media.upload
        .request(
          {
            type: "image",
            size: media.size,
            width: media.width,
            height: media.height,
          },
          file,
          (p) => {
            media.message = p + "%";
          }
        )
        .then((res) => {
          media.message = "已上传";
          media.status = 3;

          return {
            url: res.url,
            width: media.width,
            height: media.height,
            size: media.size,
          };
        })
        .catch((e) => {
          media.message = e.message || e;
          media.status = 4;
        });
    },
    startUpload() {
      console.log(this.list);
      if (this.upping) return;

      this.upping = true;

      let task = this.list.map((data) => {
        return this.addMedia(data);
      });

      Promise.all(task)
        .then((values) => {
          this.$emit("ok", values);
        })
        .catch(() => {
          this.upping = false;
        });
    },
  },
};
</script>
