<template>
  <div class="edit-channel">
    <div class="edit-panel-title">展示位置
      <span v-show="isShowZ">
        (展示到：
      <el-radio-group v-model="radio" size="small" @input="zhanShi">
        <el-radio label="1" >自己</el-radio>
        <el-radio label="2" >{{$store.state.admin.pid ? "上级" : "下级"}}</el-radio>
      </el-radio-group>)
      </span>
    </div>
    <div class="edit-panel-desc">
      单独设置封面图片可增强页面显示效果，排序数字越大越靠前展示，最多可展示三个位置
    </div>
    <div
      class="edit-panel-body"
      style="border-top: 1px solid #f1f1f1; border-bottom: 1px solid #f1f1f1"
    >
      <div v-if="list.length == 0">
        <el-empty label="当前未选择任何频道">
          <el-button type="primary" size="small" @click="addItem(-1)"
            >添加</el-button
          >
        </el-empty>
      </div>
      <div v-else class="show-channelNew" v-for="(item, index) in list">
        <div v-show="isTableShow">选择用户<span class="bi">(必选项)</span>：
          <!-- <el-select v-model="ids" placeholder="请选择一个下属部门" @change="xiaji">
            <el-option
              v-for="item in subusers"
              :key="item.id"
              :label="item.nickname"
              :value="item.id">
            </el-option>
          </el-select> -->
          <el-table
            ref="multipleTable"
            :data="subusers"
            tooltip-effect="dark"
            style="width: 100%"
            @selection-change="handleSelectionChange">
            <el-table-column
              type="selection"
              width="55">
            </el-table-column>
            <el-table-column
              prop="nickname"
              label="昵称"
              width="250">
            </el-table-column>
            <el-table-column
              prop="username"
              label="用户名"
              width="250">
            </el-table-column>
          </el-table>
          <div style="margin-top: 20px;margin-left: 500px;">
            <el-button @click="toggleSelection">确定选择</el-button>
          </div>



        </div>
        <div class="show-channel">
          <media-image v-model="item.cover_url"></media-image>
          <div style="flex: 1; margin-left: 20px">
            <div class="required-channel" v-show="radio == 1" style="margin-bottom: 10px">
              展示位置：<span class="channel-error">{{ item.error }}</span>
            </div>
            <el-channel
              v-model="item.cid"
              placeholder="请选择"
              size="small"
              v-show="radio == 1"
              @change="onChange(index)"
            ></el-channel>
            <div style="margin-bottom: 10px; margin-top: 20px">显示标题：</div>
            <el-input
              v-model="item.title"
              placeholder="请输入"
              size="small"
              maxlength="64"
            ></el-input>
            <div class="sort-channel">
              <input
                type="number"
                v-model="item.sort"
                placeholder="排序"
                title="排序：数字越大越靠前"
              />
            </div>
            <div
              class="channel-action"
              @click="addItem(index)"
              v-if="index < limit"
              style="right: 30px"
            >
              增加
            </div>
            <div class="channel-action" @click="delItem(index)">移除</div>
          </div>
        </div>
        <!-- <div class="tableBox" v-if="isTableShow">
          <mytable
            ref="table"
            :list="dataList"
            @selectfunc="selectFun"
            :api="$api.media.subUsersList"
            :query="subUsersQuery"
            @selection-change="handleSelectionChange"
          >
            <el-form class="table-toolbar" slot="toolbar">
              <div class="toolbar-header">
                <div class="toolbar-title">{{ $route.meta.title }}</div>
                <div class="toolbar-actions">
                  <el-button type="primary" size="mini" @click="onSubmit"
                    >确定</el-button
                  >
                  <el-input
                    class="search-val"
                    size="mini"
                    v-model="subUsersQuery.nickname"
                    placeholder="请输入"
                    style="width: 100px"
                    maxlength="5"
                    clearable
                  >
                  </el-input>
                  <el-button
                    size="mini"
                    icon="el-icon-search"
                    native-type="submit"
                    title="搜索"
                    circle
                  ></el-button>
                </div>
              </div>
            </el-form>
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column
              prop="id"
              label="编号"
              width="75"
              align="center"
              :formatter="formatter"
            ></el-table-column>
            <el-table-column label="标题" prop="nickname"></el-table-column>
          </mytable>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import MediaImage from "@/views/media/image/cover";
import ElEmpty from "@/components/empty/view";
import ElChannel from "@/components/media/channel/cascader";
import MediaBase from "../../../all/base";
import userRequest from "../../../../../utils/userRequest";
export default {
  components: { MediaImage, ElEmpty, ElChannel },
  extends: MediaBase,
  data() {
    return {
      subusers:[],
      ids:"",
      limit: 3,
      isTableShow: false,
      radio:"1",
      isShowZ:false,
      multipleSelection: [],
      idS:[]
    };
  },
  computed: {
    list() {
      return this.$parent.model.channels;
    },
    dataList() {
      return [];
    },
  },
  watch:{
    "radio":
      function(newV,oldV){
        // console.log(newV,oldV);
        this.$store.dispatch('admin/changeRadio',newV)
        if(newV == '2'){
          // let id = JSON.parse(localStorage.getItem('sub_Users'))[0].id
          this.isTableShow = true
          this.$api.media.subUsersList().then(res=>{
            this.subusers = res
          })
        }
        if(newV == "1"){
          this.isTableShow = false
          
        }
      },
      deep:true
  },
  methods: {
    toggleSelection() {
        this.$store.dispatch('admin/sub_User',this.idS)
      },
      handleSelectionChange(val) {
        this.multipleSelection = val;
        this.idS = this.multipleSelection.map(v=>{
          return v.id
        })
      },
    // xiaji(id){
    //   console.log(id);
    //   localStorage.setItem('sub_Users',id)

    //   // this.ids = id
    //   this.$api.media.channel
    //           .list({owner:id})
    //           .then((list)=>{
    //             console.log(list);
    //             this.$children.map(v=>{
    //             v.list = list
    //           })
    //             // this.$children[2].list = list;
    //           })
    // },
    zhanShi(index){
      // console.log(index);
      this.radio = index
      if(index == 1){
        this.$api.media.channel
              .list({owner:this.$store.state.admin.id})
              .then((list)=>{
                this.$children.map(v=>{
                v.list = list
              })
                // this.$children[2].list = list;
              })
            }else{
              this.checkList = []
            }
    },
    isParentNodeFun() {
      userRequest.get("/v1/user.account.isParentNode").then((res) => {
        this.isShowZ = res.isParentNode;
        // this.subUsersFun();
      });
      if(this.radio == "1"){
        this.isTableShow = false
      }else{
        this.isTableShow = true
      }

    },
    formatter(row) {
      row._type = this.$api.media.type[row.type];
      row._status = this.$api.media.status[row.status];
      row._pubdate = this.$utils.Date(row.pubdate).format();
      return row.id;
    },
    refresh() {
      this.$refs.table.refresh();
      this.needRefresh = 0;
    },
    onCreated() {},
    addItem(i) {
      let item = { cid: null, cover_url: "", title: "", sort: "", error: "" };

      if (i == -1) {
        this.list.push(item);
      } else {
        this.list.splice(i + 1, 0, item);
      }
      this.isParentNodeFun();
    },
    delItem(index) {
      this.list.splice(index, 1);
    },
    onChange(i) {
      let list = this.list,
        item = list[i];

      if (!item.cid) {
        item.error = "请选择";
        return;
      }

      item.error = list.some((data, index) => {
        return index != i && data.cid && data.cid == item.cid;
      })
        ? "已存在"
        : "";
    },
    validate(callback) {
      let i = 0,
        list = this.list,
        item,
        exists = [];

      while (i < list.length) {
        item = list[i];

        if (!item.cid) {
          item.error = "请选择";
          return;
        } else if (exists.indexOf(item.cid) != -1) {
          item.error = "重复";
          return;
        }

        i++;
        item.error = undefined;
        exists.push(item.cid);
      }

      callback();
    },
    // selectFun(event) {
    //   this.multipleSelection = event;
    // },
    // onSubmit() {
    //   console.log(this.multipleSelection);
    // },
  },
};
</script>
<style lang="scss">
.show-channel {
  width: 100%;
  height: 100%;
}
.show-channelNew {
  width: 100%;
  height: 100%;
}
.tableBox {
  width: 100%;
  height: auto;
}
.bi{
  color: red;
}
</style>
