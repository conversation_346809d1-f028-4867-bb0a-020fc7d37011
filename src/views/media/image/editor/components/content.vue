<template>
  <div>
    <div class="edit-panel-title">轮播内容<span style="float:right">{{ list.length }}/{{ limit }}</span></div>
    <div class="edit-panel-desc">
      至少需要添加{{ min }}条，最多可添加{{ limit }}条
      <el-button type="text" size="mini" @click="onAdd" style="float:right">添加</el-button>
    </div>
    <div class="edit-panel-body">
      <div class="preview">
        <div class="preview-content">
          <el-empty v-if="list.length==0" label="当前未添加任何内容">
            <el-button type="primary" size="small" @click="onAdd">添加</el-button>
          </el-empty>
          <div v-else style="width:100%;height:100%;background:#333">
            <el-image :src="model.url" style="width:100%;height: 100%;" fit="cover"></el-image>
            <el-input type="textarea" class="img-text" maxlength="200" autosize v-model="model.desc" placeholder="在此处输入文本..."></el-input>
          </div>
        </div>
      </div>

      <div ref="list" class="scroll-content">
        <div class="scroll-image" v-for="(item, index) in list" :class="{'is-active' : active == index}" @click="setActive(index)">
          <el-image :src="item.url" fit="fill"></el-image>
          <div class="img-index">{{ index + 1 }}</div>
          <div class="actions">
            <i class="img-action el-icon-picture-outline" title="重新选择图片" @click.stop.prevent="onReplace"></i>
            <i class="img-action el-icon-delete" title="移除" @click.stop.prevent="delItem(index)"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ElEmpty from '@/components/empty/view';
import SelectImage from '@/views/media/image/select';

export default {
  components: {ElEmpty},
  data() {
    return {
      min: 3,
      limit: 50,
      active: 0,
      model: null,
      imgopt: {size: 1024, placeholder: '建议尺寸不低于1920x1080，文件大小不超过1M'}
    }
  },
  computed: {
    list() {
      return this.$parent.model.content;
    }
  },
  created() {
    this.setActive(0);
  },
  mounted() {
    let scope = this;

    this.$require('sortable').then(() => {
      new Sortable(this.$refs.list, {
        onEnd(evt) {
          let active = evt.newIndex;
          if (active != evt.oldIndex) {
            let data = scope.list.splice(evt.oldIndex, 1);
            scope.$nextTick(function () {
              scope.list.splice(active, 0, data[0]);
              scope.setActive(active);
            });
          }
        }
      });
    });
  },
  methods: {
    validate(callback) {
      callback();
    },
    onAdd() {
      SelectImage(this.limit - this.list.length, this.imgopt).then(rows => {
        let active = this.list.length;
        rows.forEach(img => this.list.push({url: img.url, desc: ''}));
        this.setActive(active);
      });
    },
    onReplace() {
      SelectImage(1, this.imgopt).then(rows => {
        this.model.url = rows[0].url;
      });
    },
    setActive(i) {
      this.active = i;
      this.model = this.list[i];

      let $el = this.$refs.list;
      $el && this.$nextTick(function () {
        $el.scrollLeft = (i - 1) * 210;
      });
    },
    delItem(i) {
      this.list.splice(i, 1);

      let {active} = this;
      if (i <= active) {
        this.setActive(active - 1);
      }
    }
  }
}
</script>