<template>
  <el-form class="media-editor-bgmusic" ref="form" label-width="95px" label-suffix="：" size="small" :model="model" :rules="rules">
    <el-form-item label="播放地址" prop="url">
      <el-input v-model="model.url" placeholder="请输入" size="small" maxlenght="128" @change="onChange"></el-input>
      <el-button type="text" @click="uploadAudio">选择</el-button>
    </el-form-item>
    <el-form-item label="音乐名称" prop="name">
      <el-input v-model="model.name" placeholder="请输入" size="small" clearable maxlength="20"></el-input>
      <el-button type="text" @click="togglePlay">{{ playing ? '暂停' : '播放' }}</el-button>
    </el-form-item>
    <el-form-item label="默认音量" style="padding-right:10px">
      <el-slider v-model="model.volume" :min="0.1" :max="1" :step="0.1" :format-tooltip="formatVolume"></el-slider>
    </el-form-item>
    <el-form-item label="音乐设置" style="margin-bottom:0">
      <el-switch v-model="model.loop" active-text="循环" :active-value="1" :inactive-value="0" title="是否循环播放"></el-switch>
      <el-switch v-model="model.autoplay" active-text="自动" :active-value="1" :inactive-value="0" title="是否自动播放" style="margin-left:30px"></el-switch>
      <el-switch v-model="model.enabled" active-text="启用" :active-value="1" :inactive-value="0" title="是否启用" style="margin-left:30px"></el-switch>
    </el-form-item>
  </el-form>
</template>

<script>
import SelectAudio from '@/views/media/audio/select';

export default {
  props: ['model'],
  data() {
    return {
      playing: false,
      rules: {
        url: {
          required: false, trigger: 'blur', validator(rule, value, callback) {
            if (value.length > 0 && !validator.isURL(value)) {
              callback('URL格式错误');
            } else if (rule.required && value.length == 0) {
              callback('必填项');
            } else {
              callback();
            }
          }
        },
        name: {required: false, trigger: 'blur', message: '必填项'}
      }
    }
  },
  computed: {
    audio() {
      let el = document.createElement('audio');
      el.crossOrigin = 'anonymous';
      el.onerror = () => this.playing = false;
      el.onpause = () => this.playing = 0;
      el.onplay = () => this.playing = 1;
      return el;
    }
  },
  watch: {
    'model.volume': {
      immediate: true,
      handler(v) {
        this.audio.volume = v;
      }
    },
    'model.loop': {
      immediate: true,
      handler(v) {
        this.audio.loop = v;
      }
    },
    'model.enabled': {
      immediate: true,
      handler(v) {
        this.rules.url.required = !!v;
        this.rules.name.required = !!v;
      }
    }
  },
  deactivated() {
    this.audio.src = '';
    this.playing = false;
  },
  methods: {
    validate(callback) {
      this.$refs.form.validate(valid => {
        valid && callback();
      });
    },
    formatVolume(v) {
      return v == 0 ? '静音' : v == 1 ? '最大' : (v * 100 + '%');
    },
    uploadAudio() {
      return SelectAudio(1).then(list => {
        let item = list[0];

        Object.assign(this.model, {url: item.address, name: item.title});

        this.onChange();
      });
    },
    togglePlay() {
      let url = this.model.url;

      if (!url) return this.uploadAudio().then(this.togglePlay);

      if (this.playing === false) {
        this.audio.src = url;
      }

      this.playing ? this.audio.pause() : this.audio.play();
    },
    onChange() {
      let {playing} = this;
      this.audio.src = '';
      this.playing = false;

      let {url} = this.model;
      if (playing && url) {
        this.togglePlay();
      }
    }
  }
}
</script>