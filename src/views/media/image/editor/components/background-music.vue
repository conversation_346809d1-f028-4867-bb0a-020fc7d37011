<template>
  <div class="edit-base">
    <div class="edit-panel-title">背景音乐</div>
    <div class="edit-panel-body">
      <audio-panel ref="bgmusic" :model="$parent.model.bgmusic"></audio-panel>
    </div>
    <div class="edit-panel-title">语音讲解</div>
    <div class="edit-panel-body">
      <audio-panel ref="bgvoice" :model="$parent.model.bgvoice"></audio-panel>
    </div>
  </div>
</template>

<script>
import AudioPanel from './audio-panel';

export default {
  components: {AudioPanel},
  methods: {
    validate(callback) {
      this.$refs.bgmusic.validate(() => {
        this.$refs.bgvoice.validate(callback);
      });
    }
  }
}
</script>