<template>
  <div>
    <mytable ref="table" api="/v1/company.staff.volunteer.list" :list="list">
      <div class="table-toolbar" slot="toolbar">
        <div class="toolbar-header">
          <div class="toolbar-title">{{ $route.meta.title }}</div>
          <div class="toolbar-actions">
            <el-button type="primary" size="mini" @click="onAdd">添加</el-button>
          </div>
        </div>
      </div>
      <el-table-column label="编号" prop="id" align="center"></el-table-column>
      <el-table-column label="标题" prop="content" align="center"></el-table-column>
      <el-table-column label="照片地址" prop="image_url" align="center"></el-table-column>
      <el-table-column label="排序" prop="sort" align="center"></el-table-column>
      <el-table-column label="操作" width="90" class-name="table-action">
        <template slot-scope="scope">
          <el-button class="btn-action" size="mini" @click="onEdit(scope.row)" title="编辑">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
            </svg>
          </el-button>
          <el-button class="btn-action" size="mini" @click="onDelete(scope.row)" title="删除">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#delete"></use>
            </svg>
          </el-button>
        </template>
      </el-table-column>
    </mytable>
    <el-dialog :title="flag ? '添加志愿服务' : '编辑志愿服务'" :visible.sync="dialogVisible" width="800px" @closed="onClosed"
               :append-to-body="true" :close-on-click-modal="false">
      <el-form ref="form" label-width="80px" :model="model" :rules="rules">
        <el-form-item label="标题" prop="content">
          <el-input v-model.trim="model.content" placeholder="必填项"></el-input>
        </el-form-item>
        <el-form-item label="照片" prop="image_url">
          <cover-image v-model="model.image_url" style="width:400px;height:233px;" empty-text="志愿服务照片"></cover-image>
        </el-form-item>
        <el-form-item label="记录人" prop="staff_id">
          <el-select v-model="model.staff_id" @change="personChange" filterable allow-create default-first-option>
            <el-option v-for="(item, index) in person" :key="index" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input v-model="model.sort"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSubmit">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import request from "@/api/modules/request.js"
import CoverImage from "@/views/media/image/cover";
export default {
  name: 'volunteer',
  components: { CoverImage },
  data() {
    return {
      list: [],
      flag: true,
      dialogVisible: false,
      model: {
        staff_id: null,
        content: '',
        image_url: '',
        sort: null
      },
      person: []
    };
  },
  created() {
    request.get('/v1/company.staff.list').then(res => {
      this.person = res
    })
  },
  mounted() {
  },
  methods: {
    validate(callback) {
      this.$refs.form.validate(valid => {
        valid && callback();
      });
    },
    refresh() {
      this.model = {
        staff_id: null,
        content: '',
        image_url: '',
        sort: null
      }
      this.$refs.table.refresh();
    },
    onAdd() {
      this.flag = true
      this.model.id = 0
      this.model.content = ''
      this.model.image_url = ''
      this.model.staff_id = null
      this.model.sort = null
      this.dialogVisible = true
    },
    onSubmit() {
      if (this.flag) {
        // 添加
        request.post('/v1/company.staff.volunteer.create', this.model).then(res => {
          this.dialogVisible = false
          this.refresh()
        })
      } else {
        // 修改
        request.post('/v1/company.staff.volunteer.update', this.model).then(res => {
          this.dialogVisible = false
          this.refresh()
        })
      }
    },
    onEdit(row) {
      this.flag = false
      this.dialogVisible = true
      this.model = row
    },
    onDelete(row) {
      this.$confirm('此操作将永久删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        request.post('/v1/company.staff.volunteer.delete', { id: row.id }).then(res => {
          this.refresh()
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    }
  },
};
</script>

<style lang="scss" scoped></style>