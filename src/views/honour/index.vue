<template>
  <div>
    <mytable ref="table" api="/v1/company.honor.list" :list="list">
      <div class="table-toolbar" slot="toolbar">
        <div class="toolbar-header">
          <div class="toolbar-title">{{ $route.meta.title }}</div>
          <div class="toolbar-actions">
            <!-- <el-input class="search-val" size="mini" v-model="query.code" placeholder="请输入编号" style="width: 200px"
                      maxlength="5" clearable>
                      <div></div>
            </el-input>
            <el-button size="mini" icon="el-icon-search" title="搜索" circle @click="searchFun"></el-button> -->
            <el-button type="primary" size="mini" @click="onAdd">添加</el-button>
          </div>
        </div>
      </div>
      <el-table-column label="编号" prop="id" align="center"></el-table-column>
      <el-table-column label="荣誉名称" prop="name" align="center"></el-table-column>
      <el-table-column label="获得时间" prop="honor_date" align="center"></el-table-column>
      <el-table-column label="简介" prop="content" align="center" width="700px"
                       :show-overflow-tooltip="true"></el-table-column>
      <el-table-column label="操作" width="90" class-name="table-action">
        <template slot-scope="scope">
          <el-button class="btn-action" size="mini" @click="onEdit(scope.row)" title="编辑">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
            </svg>
          </el-button>
          <el-button class="btn-action" size="mini" @click="onDelete(scope.row)" title="删除">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#delete"></use>
            </svg>
          </el-button>
        </template>
      </el-table-column>
    </mytable>
    <el-dialog :title="flag ? '添加荣誉' : '编辑荣誉'" :visible.sync="dialogVisible" width="800px" @closed="onClosed"
               :append-to-body="true" :close-on-click-modal="false">
      <el-form ref="form" label-width="80px" :model="model" :rules="rules">
        <el-form-item label="荣誉名称" prop="name">
          <el-input v-model.trim="model.name" placeholder="必填项"></el-input>
        </el-form-item>
        <el-form-item label="荣誉图片" prop="honor_image">
          <cover-image v-model="model.honor_image" style="width:400px;height:233px;" empty-text="荣誉图片"></cover-image>
        </el-form-item>
        <el-form-item label="获得时间" prop="honor_date">
          <el-date-picker v-model="model.honor_date" type="date" placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="简介" prop="content">
          <el-input v-model.trim="model.content" type="textarea" :rows="5" maxlength="500" placeholder="请输入"
                    show-word-limit></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSubmit">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import CoverImage from "@/views/media/image/cover";
export default {
  name: 'honour',
  components: { CoverImage },
  data() {
    return {
      list: [
      ],
      flag: true,
      dialogVisible: false,
      model: {
        name: '',
        content: '',
        honor_image: '',
        honor_date: '',
      },
      rules: {
        name: { required: true, trigger: 'blur', message: '必填项' },
        content: { required: true, trigger: 'blur', message: '必填项' },
        honor_image: { required: true, trigger: 'blur', message: '必填项' },
        honor_date:
          { required: true, trigger: 'blur', message: '必填项' },
      }
    };
  },
  created() {
  },
  mounted() {
  },
  methods: {
    validate(callback) {
      this.$refs.form.validate(valid => {
        valid && callback();
      });
    },
    refresh() {
      this.model = {
        name: '',
        content: '',
        honor_image: '',
        honor_date: '',
      }
      this.$refs.table.refresh();
    },
    onAdd() {
      this.flag = true
      this.model.content = ''
      this.model.name = ''
      this.model.honor_image = ''
      this.model.honor_date = ''
      this.dialogVisible = true
    },
    onSubmit() {
      this.validate(this.doSubmit);
    },
    doSubmit() {
      if (this.flag) {
        this.$api.post('/v1/company.honor.create', this.model).then(res => {
          this.dialogVisible = false
          this.refresh()
        })
      } else {
        this.$api.post('/v1/company.honor.update', this.model).then(res => {
          this.dialogVisible = false
          this.refresh()
        })
      }
    },
    onEdit(row) {
      this.flag = false
      this.dialogVisible = true
      this.model = row
    },
    onDelete(row) {
      this.$confirm('此操作将永久删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.post('/v1/company.honor.delete', { id: row.id }).then(res => {
          this.refresh()
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    }
  },
  beforeDestroy() {
    /**
     * 
     * 
     * 
     * 
     */
  },
};
</script>

<style lang="scss" scoped></style>