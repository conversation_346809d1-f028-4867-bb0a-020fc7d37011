<template>
  <el-card class="box-card dict-item-container">
    <div slot="header" class="clearfix">
      <template v-if="current">
        {{ current.name }} - {{ current.code }}
        <el-button style="float:right;padding: 3px 0" type="text" @click="onAdd()">加值</el-button>
      </template>
      <template v-else>参考值</template>
    </div>

    <el-input placeholder="搜索Code" size="mini" suffix-icon="el-icon-search"></el-input>

    <el-table :data="$parent.items" size="small" class="dict-item-table" row-key="code" highlight-current-row>
      <el-table-column prop="code" label="Code"></el-table-column>
      <el-table-column prop="name" label="Name"></el-table-column>
      <el-table-column prop="pinyin" label="拼音"></el-table-column>
      <el-table-column prop="state" label="状态" align="center" :formatter="fState"></el-table-column>
      <el-table-column prop="sort" label="排序" align="center"></el-table-column>
      <el-table-column label="操作" width="90" class-name="table-action">
        <template slot-scope="scope">
          <el-button class="btn-action" size="mini" @click="$parent.editItem(scope.row)" title="编辑">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
            </svg>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-card>
</template>

<script>

export default {
  data() {
    return {
      limit: 30,
      page: 0
    }
  },
  computed: {
    current() {
      return this.$parent.current
    }
  },
  watch: {
    current(v) {
      if (v) {
        this.loadPage(1);
      } else {
        this.list = [];
      }
    }
  },
  methods: {
    loadPage(page) {
      this.$api.get('/v1/sys.dictionary.items', {type: this.current.code}).then(rows => {
        this.$parent.items = rows;
      });
    },
    fState(row, column, val) {
      return val === 1 ? '启用中' : val === 0 ? '已禁用' : '已删除'
    },
    onAdd() {
      this.$parent.editItem({
        type: this.current.code
      });
    }
  }
}
</script>