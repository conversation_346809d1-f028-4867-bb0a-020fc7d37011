<template>
  <el-card class="box-card dict-type-container">
    <div slot="header" class="clearfix">
      <span>数据类别</span>
      <el-button style="float: right; padding: 3px 0" type="text" @click="$parent.editType()">添加</el-button>
    </div>

    <el-input placeholder="搜索Code" size="mini" suffix-icon="el-icon-search" style="flex:1"></el-input>

    <el-table :data="$parent.types" size="small" class="dict-type-table" highlight-current-row @current-change="onCurrentChange">
      <el-table-column prop="code" label="Code"></el-table-column>
      <el-table-column prop="name" label="Name"></el-table-column>
      <el-table-column prop="type" label="Type" :formatter="fType" width="60px"></el-table-column>
      <el-table-column label="操作" width="50" class-name="table-action">
        <template slot-scope="scope">
          <el-button class="btn-action" size="mini" @click="$parent.editType(scope.row)" title="编辑">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
            </svg>
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div style="text-align: center; color: #f0f0f0;">
      <el-button v-if="hasMore" type="text" size="mini" @click="loadPage(page + 1)">加载更多</el-button>
      <div v-else style="margin-top:15px">没有更多了</div>
    </div>
  </el-card>
</template>

<script>

export default {
  data() {
    return {
      page: 0,
      hasMore: true
    }
  },
  computed: {
    current: {
      get() {
        return this.$parent.current
      },
      set(v) {
        this.$parent.current = v;
      }
    }
  },
  created() {
    this.loadPage(1);
  },
  methods: {
    loadPage(page) {
      let limit = 30;
      let offset = (page - 1) * limit;

      this.$api.get('/v1/sys.dictionary.types', {offset: offset, limit: limit}).then(list => {
        let current = this.current && this.current.code;
        let exists = current ? list.some(item => {
          return item.code == current;
        }) : false;

        if (!exists) {
          this.current = list[0];
        }

        this.$parent.types = list;
        this.page = page;

        this.hasMore = list.length >= limit;
      });
    },
    fType(row, column, value) {
      return value == 1 ? '数字' : '字符串'
    },
    onCurrentChange(row) {
      this.current = row;
    }
  }
}
</script>