<template>
  <el-dialog :visible.sync="visible"
             :append-to-body="true" :close-on-press-escape="false"
             width="500px" custom-class="dialog-form abs-close"
             @closed="$emit('closed')">
    <template slot="title">
      <span class="el-dialog__title">{{ $parent.current.name + ' - ' + $parent.current.code }}</span>
      <div class="el-dialog__headerbtn">
        <el-switch v-model="model.state" :active-value="1" :inactive-value="0" title="是否启用"></el-switch>
      </div>
    </template>

    <el-form label-width="85px" size="small">
      <el-form-item prop="code" label="Code">
        <el-input placeholder="代码标识" v-model="model.code" maxlength="20" :disabled="!!model.id"></el-input>
        <el-checkbox class="dist-leaf-checkbox" v-model="model.leaf" :true-label="1" :false-label="0" title="勾选后不允许添加下级">子页</el-checkbox>
      </el-form-item>
      <el-form-item prop="name" label="Name">
        <el-input placeholder="显示文本" v-model="model.name" maxlength="32"></el-input>
      </el-form-item>
      <el-form-item prop="pinyin" label="拼音">
        <el-input placeholder="中文拼音" v-model="model.pinyin" maxlength="32"></el-input>
      </el-form-item>
      <el-form-item prop="name" label="排序">
        <el-input placeholder="数字越大越靠前" v-model="model.sort" maxlength="4"></el-input>
      </el-form-item>
      <el-form-item prop="parent" label="上级">
        <el-input placeholder="代码标识" v-model="model.parent" maxlength="20"></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button type="danger" v-if="model.id" @click="onDelete">删 除</el-button>
      <el-button v-else @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="submit">保 存</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: ['value'],
  data() {
    return {
      visible: true,
      model: Object.assign({
        type: '',
        code: '',
        name: '',
        pinyin: '',
        sort: '',
        state: 1,
        parent: '',
        leaf: 1
      }, this.value)
    }
  },
  methods: {
    submit() {
      this.visible = false;

      let {model} = this;
      let url = '/v1/sys.dictionary.' + (model.id ? 'update' : 'create') + '?action=item';
      this.$api.post(url, model).then(res => {
        let list = this.$parent.items;

        list.some(item => {
          if (item.id == res.id) {
            Object.assign(item, res);
            return true;
          }
        }) || list.push(res);
      });
    },
    onDelete() {
      this.$confirm('操作不可恢复，是否继续', '提示', {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.visible = false;
        let list = this.$parent.items;
        let {id} = this.model;

        this.$api.post('/v1/sys.dictionary.delete?action=item', {id: id}).then(_ => {
          list.some((item, index) => {
            if (item.id == id) {
              list.splice(index, 1);
              return true;
            }
          });
        });
      });
    }
  }
}
</script>