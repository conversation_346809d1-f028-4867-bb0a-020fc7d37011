<template>
  <div class="dict-page">
    <dict-type ref="type"></dict-type>
    <dict-item ref="item"></dict-item>

    <edit-type v-if="showEditType" v-model="editing" @closed="showEditType=false"></edit-type>
    <edit-item v-if="showEditItem" v-model="editing" @closed="showEditItem=false"></edit-item>
  </div>
</template>

<script>
import DictType from './type';
import EditType from './components/edit-type';
import DictItem from './item';
import EditItem from './components/edit-item';

export default {
  name: 'DictionaryPage',
  components: {DictType, EditType, DictItem, EditItem},
  data() {
    return {
      current: null,
      types: [],
      items: [],
      editing: null,
      showEditType: false,
      showEditItem: false
    }
  },
  methods: {
    editType(row) {
      this.editing = row;
      this.showEditType = true;
    },
    editItem(row) {
      this.editing = row;
      this.showEditItem = true;
    }
  }
}
</script>

<style lang="scss">
.dict-page {
  .el-table {
    margin-top: 15px;
  }

  .el-card__header {
    font-size: 15px;
  }
}

.dict-type-container {
  float: left;
  width: 480px;
}

.dict-item-container {
  margin-left: 500px;
}

.dist-leaf-checkbox {
  position: absolute;
  right: 15px;
  top: 0;
}
</style>