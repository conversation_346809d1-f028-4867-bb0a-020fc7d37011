<template>
  <div class="menu-container">
    <mytable ref="table" :api="$api.menu.query" :list="menus">
      <div class="table-toolbar" slot="toolbar">
        <div class="toolbar-header">
          <div class="toolbar-title">菜单列表</div>
          <div class="toolbar-actions">
            <el-button type="primary" size="mini" @click="handleEdit()">添加</el-button>
          </div>
        </div>
      </div>

      <el-table-column prop="title2" label="菜单名称" width="180"></el-table-column>

      <el-table-column prop="nodes" label="功能权限">
        <template slot-scope="scope">
          <el-tag size="small" type="info" @click.native="addAPI(scope.row)">添加</el-tag>
          <template v-if="scope.row.nodes">
            <el-tag size="small" v-for="node in scope.row.nodes" :key="node.id" @click.native="handleEdit(node)">{{node.title}}</el-tag>
          </template>
        </template>
      </el-table-column>

      <el-table-column prop="disabled" label="状态" width="90" align="center">
        <template slot-scope="scope">
          <span :class="`menu-status-${scope.row.disabled}`"></span>{{fStatus(scope.row.disabled)}}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="90" class-name="table-action">
        <template slot-scope="scope">
          <el-button class="btn-action" size="mini" @click="handleEdit(scope.row)" title="编辑">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
            </svg>
          </el-button>
          <el-button class="btn-action" size="mini" @click="handleDelete(scope.row)" title="删除">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#delete"></use>
            </svg>
          </el-button>
        </template>
      </el-table-column>
    </mytable>

    <!-- 编辑 -->
    <el-dialog v-if="model" :visible.sync="dialogVisible" width="600px" v-on:closed="cancel" class="menu-edit-dialog" :close-on-click-modal="false">
      <template slot="title">
        <span class="el-dialog__title">{{dialogTitle}}</span>
        <div class="el-dialog__headerbtn">
          <el-switch v-model="model.disabled" :active-value="0" :inactive-value="1" title="是否禁用"></el-switch>
        </div>
      </template>

      <el-form ref="form" label-width="55px" :model="model" :rules="rules" v-loading="loading">
        <el-form-item label="菜单" prop="pid">
          <el-select v-model.number="model.pid" filterable placeholder="请选择">
            <el-option label="一级菜单" :value="0" v-if="model.type==0"></el-option>
            <el-option v-for="item in menus" :key="item.id" :label="item.title2" :value="item.id"></el-option>
          </el-select>
        </el-form-item>

        <el-row>
          <el-col :span="12">
            <el-form-item label="名称" prop="title">
              <el-input v-model="model.title" placeholder="请输入" maxlength="6"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sort">
              <el-input v-model.number="model.sort" placeholder="正序排序" maxlength="3" title="正序排序，数字越小越靠前"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="标识" prop="alias">
          <el-input v-model="model.alias" placeholder="唯一权限标识" maxlength="32"></el-input>
        </el-form-item>

        <el-form-item label="跳转">
          <el-input v-model="model.path" placeholder="跳转链接" maxlength="128"></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" v-if="!model.type">取 消</el-button>
        <el-button @click="handleDelete(model)" v-else>删 除</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
  export default {
    name: 'MenuPage',
    data() {
      return {
        dialogVisible: false,
        loading: false,
        menus: [],
        model: null,
        rules: {
          pid: {required: true, message: '必选项', trigger: 'blur'},
          title: {required: true, message: '必填项', trigger: 'blur'},
          alias: {required: true, message: '必填项', trigger: 'blur'},
          sort: {required: true, type: 'number', message: '格式错误', trigger: 'blur'}
        },
        dialogTitle: ''
      }
    },
    methods: {
      fStatus(val) {
        return val == 1 ? '已禁用' : '启用中'
      },
      assign(row) {
        row = row || {};

        return {
          id: row.id,
          type: row.type || 0,
          pid: row.pid || 0,
          title: row.title || '',
          sort: row.sort || 100,
          path: row.path || '',
          disabled: row.disabled === 1 ? 1 : 0,
          alias: row.alias || ''
        };
      },
      addAPI(row) {
        this.handleEdit({type: 1, pid: row.id});
      },
      handleEdit(row) {
        let model = this.assign(row);
        this.dialogTitle = (model.id ? '修改' : '添加') + (model.type == 0 ? '菜单' : '节点');
        this.model = model;
        this.dialogVisible = true;
      },
      cancel() {
        if (this.loading) {
          this.$message('数据提交中....');
        } else {
          this.$refs.form.resetFields();
        }
      },
      submit() {
        if (this.loading) {
          return this.$message({type: 'warning', message: '正在请求中...'});
        }

        this.$refs.form.validate(valid => {
          if (!valid) return;

          let model = Object.assign({}, this.model);
          this.loading = true;
          this.$api.menu[model.id ? 'update' : 'create'](model).then(() => {
            this.$refs.table.refresh();
          }).finally(() => {
            this.dialogVisible = false;
            this.loading = false;
          });
        });
      },
      handleDelete(row) {
        if (row.children) {
          return this.$message.error('请先删除下级菜单');
        }

        this.$confirm('操作不可恢复，确定删除吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.dialogVisible = false;
          return this.$api.menu.delete(row.id);
        }).then(() => {
          this.$refs.table.refresh();
        });
      }
    }
  }
</script>

<style lang="scss">
  .menu-container {
    .el-tag {
      cursor: pointer
    }

    .el-dialog .el-select {
      display: block
    }

    .el-dialog__body {
      padding: 35px 50px 0 30px;
    }

    button.el-dialog__headerbtn {
      position: absolute;
      right: -12px;
      top: -12px;
      border-radius: 50%;
      background: #f0f0f0;
      width: 24px;
      height: 24px;
    }

    .menu-status-0, .menu-status-1 {
      display: inline-block;
      width: 4px;
      height: 4px;
      border-radius: 50%;
      vertical-align: middle;
      margin: -1px 4px 0 0;
    }

    .menu-status-0 {
      background-color: #67C23A
    }

    .menu-status-1 {
      background-color: #F56C6C
    }
  }
</style>
