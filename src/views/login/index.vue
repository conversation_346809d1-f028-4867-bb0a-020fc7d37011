<template>
  <div class="login-page">
    <component v-bind:is="view" @change="setType" @submit="submit" :loading="loading"></component>
  </div>
</template>

<style lang="scss" src="./style.scss"></style>

<script>
import LoginByPassword from "./components/password";
import LoginByCaptcha from "./components/captcha";
import LoginByWeixin from "./components/weixin";

export default {
  components: { LoginByPassword, LoginByCaptcha, LoginByWeixin },
  data() {
    return {
      view: "login-by-password",
      loading: 0,
    };
  },
  methods: {
    setType(v, agree) {
      return this.$message({
        message: "请使用密码登录",
        type: "warning",
      });

      // if (v == "weixin" && !agree) {
      //   return this.$message({
      //     message: "请阅读并同意用户使用协议",
      //     type: "warning",
      //   });
      // }

      // this.view = "login-by-" + v;
    },
    submit(model) {
      this.loading = 1;

      let query = this.$route.query;

      this.$store
        .dispatch("admin/login", model)
        .then((res) => {
          console.log(res);
          location.replace(
            // query.redirect ? decodeURIComponent(query.redirect) : "/homePage"
            res.id == 1 ? "/homePage" : "/app/home/<USER>"
          );

          request.get('/v1/sys.access.nodes').then(res => {
            console.log(res);
            let operation = res.some(v => {
              return v === "media:item:operation"
            })
            this.$store.dispatch('admin/operation', operation)
          })
        })
        .finally(() => {
          this.loading = 0;
        });
    },
  },
};
</script>
