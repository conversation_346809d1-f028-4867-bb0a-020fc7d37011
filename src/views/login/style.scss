.login-page {
  width: 100vw;
  height: 100vh;
  background: #fff;

  .login-container {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 540px;
    padding: 40px;
    box-shadow: 0 0 20px #304156;
  }

  .el-alert {
    margin-top: -60px;
  }

  .el-input__inner {
    border: none;
    border-radius: 0;
    border-bottom: 1px solid #DCDFE6;
    padding: 0;
    font-size: 16px;
  }

  .el-form-item {
    margin-bottom: 40px;
  }

  .login-button {
    width: 100%;
    margin-top: 20px;
  }

  .forget {
    display: none;
    padding-right: 5px;
    margin-right: 5px;
    border-right: 1px solid #e5e5e5;
  }

  .login-header {
    height: 40px;
    margin-bottom: 44px;
  }

  .login-type {
    font-size: 22px;
    color: #999;

    .active {
      color: #333;
      font-weight: bold;
    }
  }

  .login-label {
    cursor: pointer;

    & + .login-label {
      padding-left: 8px;
      margin-left: 8px;
      border-left: 1px solid #e5e5e5;
    }
  }

  .switch-type {
    position: absolute;
    right: 0;
    top: 0;
  }

  .switch-label {
    background-color: rgba(51, 136, 255, .13);
    color: #38f;
    border: 1px solid #38f;
    padding: 5px 8px;
    white-space: nowrap;
    position: absolute;
    right: 100%;
    top: 0;
    margin-right: 6px;

    &:before, &:after {
      content: " ";
      display: block;
      position: absolute;
      left: 100%;
      top: 50%;
      margin-top: -6px;
    }

    &:before {
      border: 6px solid transparent;
      border-left-color: #38f;
      z-index: 1;
    }

    &:after {
      border: 6px solid transparent;
      border-left-color: #e5f0ff;
      transform: translateX(.5px);
      margin-left: -2px;
      z-index: 2;
    }
  }

  .qrcode, .bypwd {
    width: 40px;
    height: 40px;
    cursor: pointer;
  }

  .qrcode {
    background: no-repeat 100%/100% url(cdn("sys/login-2.png"));
  }

  .bypwd {
    background: no-repeat 100%/100% url(cdn("sys/login-1.png"));
  }

  .login-body {
    position: relative;
  }

  .login-footer {
    line-height: 24px;
    margin-top: 20px;
  }

  .autologin {
    padding-top: 26px;
  }

  .toggle-pwd {
    cursor: pointer;
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    color: #C0C4CC;
    padding: 0 15px;
    font-size: 24px;
  }

  .mobile input {
    padding-left: 100px;
  }

  .area-select {
    z-index: 1;
    position: absolute;
    top: 0;
    left: 0;
    font-weight: bold;
    pointer-events: none;

    &:after {
      content: " ";
      border-right: solid 2px #333;
      border-top: solid 2px #333;
      position: absolute;
      top: 50%;
      right: -16px;
      width: 4px;
      height: 4px;
      transform: translateY(-50%) rotate(135deg);
      margin-top: -1px;
    }
  }

  .getsms {
    position: absolute;
    right: 16px;
    font-size: 16px;
  }

  .qrimg {
    width: 160px;
    height: 160px;
    display: block;
    margin: auto;
  }

  .login-container.is-weixin {
    .login-footer {
      text-align: center;
    }

    .login-body {
      height: 266px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .refresh-qrcode {
    z-index: 1;
    background: rgba(255, 255, 255, 0.8);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    text-align: center;
    line-height: 2;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .scanmsg {
    text-align: center;
    margin-top: 20px;
  }
}