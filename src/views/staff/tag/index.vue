<template>
  <div class="staff-group-page">
    <mytable ref="table" :api="apiUrl">
      <div class="table-toolbar" slot="toolbar">
        <div class="toolbar-header">
          <div class="toolbar-title">{{ $route.meta.title }}</div>
          <div class="toolbar-actions">
            <template v-if="showSort">
              <el-button size="mini" @click="showSort=false">取消</el-button>
              <el-button type="primary" size="mini" @click="saveSort">保存</el-button>
            </template>
            <template v-else>
              <el-button size="mini" @click="showSort=true">排序</el-button>
              <el-button type="primary" size="mini" @click="onAdd">添加</el-button>
            </template>
          </div>
        </div>
      </div>

      <el-table-column label="分组名称" prop="name"></el-table-column>
      <el-table-column label="显示顺序" prop="sort" align="center" width="60px"></el-table-column>
      <el-table-column label="成员数量" prop="people" align="center"></el-table-column>
      <el-table-column label="创建时间" prop="created" align="center" :formatter="fCreated"></el-table-column>
      <el-table-column label="显示到组织架构" prop="hidden" align="center" :formatter="fHidden"></el-table-column>
      <el-table-column label="显示排序" v-if="showSort" prop="sort" align="center" width="90" class-name="is-sort">
        <input slot-scope="scope" v-model="scope.row.sort" type="text">
      </el-table-column>
      <el-table-column v-else label="操作" width="90" class-name="table-action">
        <template slot-scope="scope">
          <el-button class="btn-action" size="mini" @click="onEdit(scope.row)" title="编辑">
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#edit"></use>
            </svg>
          </el-button>
          <el-button class="btn-action" size="mini" @click="onDelete(scope.row.id)" title="删除">
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#delete"></use>
            </svg>
          </el-button>
        </template>
      </el-table-column>
    </mytable>

    <edit-dialog :visible.sync="showEdit" :editing="editing" @saved="refresh"></edit-dialog>
  </div>
</template>

<script>
import EditDialog from './components/edit-dialog';

export default {
  name: 'StaffGroupPage',
  components: {EditDialog},
  data() {
    return {
      showEdit: false,
      editing: null,
      showSort: false
    }
  },
  computed: {
    apiUrl() {
      return '/v1/company.staff.tag.list?company_id=' + this.$store.state.admin.company_id
    }
  },
  methods: {
    onAdd() {
      this.editing = null;
      this.showEdit = true;
    },
    onEdit(data) {
      this.editing = data;
      this.showEdit = true;
    },
    onDelete(id) {
      this.$confirm('确定要移除所有成员并删除此分组吗？', '提示', {
        confirmButtonText: '删除',
        type: 'warning'
      }).then(() => {
        return this.$api.post('/v1/company.staff.tag.delete', {id: id});
      }).then(this.refresh);
    },
    fCreated(row, field, val) {
      return this.$utils.Date(val).format();
    },
    fHidden(row, field, val) {
      return val ? '否' : '是';
    },
    refresh() {
      this.$nextTick(this.$refs.table.refresh);
    },
    saveSort() {
      let list = {};

      this.$refs.table.list.forEach(item => {
        if (/^\d+$/.test(item.sort)) {
          list[item.id] = parseInt(item.sort);
        }
      });

      this.showSort = false;
      this.$api.post('/v1/company.staff.tag.sort', list).then(this.refresh);
    }
  }
}
</script>

<style lang="scss">
.edit-staff-group-dialog {

  .el-dialog__body {
    padding: 30px 70px 0 60px;
  }

}
</style>