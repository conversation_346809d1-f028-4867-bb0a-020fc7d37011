<template>
  <div class="edit-panel-content">
    <!-- <div></div> -->
    <div class="btn" style="width:100%;text-align:right;">
      <el-button size="mini" style="margin:10px 0;" @click="onAdd">添加</el-button>
    </div>
    <mytable ref="table" api="/v1/company.staff.resume.list" :query="query" :list="list">
      <el-table-column label="阶段" prop="name" align="center" width="100px"></el-table-column>
      <el-table-column label="内容" prop="content" align="center" width="400px"></el-table-column>
      <el-table-column label="时间" prop="resume_date" align="center" width="120px"></el-table-column>

      <el-table-column label="操作" width="90" class-name="table-action">
        <template slot-scope="scope">
          <el-button class="btn-action" size="mini" @click="onEdit(scope.row)" title="编辑">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
            </svg>
          </el-button>
          <el-button class="btn-action" size="mini" @click="onDelete(scope.row)" title="删除">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#delete"></use>
            </svg>
          </el-button>
        </template>
      </el-table-column>
    </mytable>


    <el-dialog :title="flag ? '添加内容' : '编辑内容'" :visible.sync="dialogVisible" width="500px" @closed="onClosed"
               :append-to-body="true" :close-on-click-modal="false">
      <el-form ref="form" label-width="80px" :model="growth" :rules="rules">
        <el-form-item label="阶段" prop="name">
          <el-input v-model.trim="growth.name" placeholder="如：积极分子"></el-input>
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input v-model.trim="growth.content" type="textarea" :rows="5" maxlength="500" placeholder="如：于2023年成为积极分子"
                    show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="时间" prop="resume_date">
          <el-date-picker v-model="growth.resume_date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date"
                          placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSubmit">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

export default {
  data() {
    return {
      query: {
        staffId: this.$route.params.user
      },
      list: [],
      dialogVisible: false,
      growth: {
        name: '',
        resume_date: '',
        content: '',
        staff_id: this.$route.params.user
      },
      rules: {
        name: { required: true, trigger: 'blur', message: '必填项' },
        content: { required: true, trigger: 'blur', message: '必填项' },
        resume_date:
          { required: true, trigger: 'blur', message: '必填项' },
      }
    }
  },
  methods: {
    validate(callback) {
      this.$refs.form.validate(valid => {
        valid && callback();
      });
    },
    refresh() {
      this.growth = {
        name: '',
        content: '',
        resume_date: '',
        staff_id: this.$route.params.user
      }
      this.$refs.table.refresh();
    },
    onAdd() {
      this.flag = true
      this.growth.resume_date = ''
      this.growth.content = ''
      this.growth.name = ''
      this.dialogVisible = true
    },
    onSubmit() {
      this.validate(this.doSubmit);
    },
    doSubmit() {
      if (this.flag) {
        // 添加
        this.$api.post('/v1/company.staff.resume.create', this.growth).then(res => {
          this.dialogVisible = false
          this.refresh()
        })
      } else {
        // 修改
        this.$api.post('/v1/company.staff.resume.update', this.growth).then(res => {
          this.dialogVisible = false
          this.refresh()
        })
      }
    },
    onEdit(row) {
      this.flag = false
      this.dialogVisible = true
      this.growth = { ...row }
    },
    onDelete(row) {
      this.$confirm('此操作将永久删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.post('/v1/company.staff.resume.delete', { id: row.id }).then(res => {
          this.refresh()
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    }
  }
}
</script>