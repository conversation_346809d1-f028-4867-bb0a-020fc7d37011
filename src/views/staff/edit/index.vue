<template>
  <div class="edit-panel-page">
    <div class="edit-panel-box">
      <div class="edit-panel-side">
        <div class="edit-panel-name">组员资料</div>

        <cover-image v-model="model.headimg" style="margin:30px auto auto auto;width:173px;height:233px;"
                     empty-text="一寸照"></cover-image>

        <div class="edit-panel-menu">
          <div class="item" v-for="item in menu" :class="{ active: active == item.name }" @click="go(item.name)">
            <div class="label">{{ item.label }}</div>
            <div class="value">
              {{ item.value }}<i class="el-icon-arrow-right"></i>
            </div>
          </div>
        </div>
      </div>
      <component ref="part" v-bind:is="`edit-${active}`"></component>
    </div>
  </div>
</template>

<style lang="scss" src="../style.scss"></style>

<script>
import CoverImage from '@/views/media/image/cover';
import EditBase from './components/base';
import EditParty from './components/party';
import EditGrowth from './components/growth'

export default {
  components: { CoverImage, EditBase, EditParty, EditGrowth },
  data() {
    let date = this.$utils.Date(parseInt(Date.now() / 1000) - 20 * 365 * 86400).format('YYYY') + '-01-01';

    return {
      loading: false,
      active: 'base',
      menu: [
        { label: '基本信息', value: '', name: 'base' },
        { label: '党员信息', value: '', name: 'party' },
        { label: '成长历程', value: '', name: 'growth' },

      ],
      model: {
        company_id: 0,
        headimg: '',
        name: '',
        sex: 1,
        sequence: '',
        status: 1,
        birthday: date,
        nation: 0,
        school_name: '',
        education: 0,
        join_work: '',
        position_name: '',
        party_code: 1,
        party_join: null,
        party_title: '',
        party_oath: '',
        party_intro: '',
        dept_id: null,
        tag: [],
        grade: 0,
        promise_image: ''
      }
    }
  },
  computed: {
    isAdd() {
      return this.$route.name == 'staff.add';
    },
    computed: {
      current() {
        for (let list = this.menu, i = 0, len = list.length; i < len; i++) {
          if (list[i].name == this.active) {
            return i;
          }
        }
      }
    }
  },
  created() {
    this.reload();
  },
  methods: {
    reload() {
      let { company, user } = this.$route.params;

      if (this.isAdd) {
        this.model.company_id = parseInt(company);
      } else {
        this.loading = true;
        this.$api.get('/v1/company.staff.get', { id: user, company_id: company }).then(res => {
          Object.assign(this.model, res);
          this.loading = false;
        });
      }
    },
    prev() {
      this.go(this.menu[this.current - 1].name);
    },
    next() {
      this.go(this.menu[this.current + 1].name);
    },
    go(name) {
      if (this.active == name) return;

      if (name != 'base' && !this.model.id) {
        this.$message.error('请先保存基本信息');
      } else {
        this.active = name;
      }
    },
    validate(callback) {
      return new Promise(resolve => {
        this.$refs.part.validate(function () {
          resolve(callback());
        })
      })
    },
    submit(url, body) {
      if (this.loading) return;

      this.loading = true;
      this.$api.post(url, body).then(res => {
        if (this.isAdd) {
          this.$router.replace({ name: 'staff.edit', params: { company: res.company_id, user: res.id } });
        }

        this.reload();
      }).finally(() => {
        this.loading = false;
      });
    }
  }
}
</script>