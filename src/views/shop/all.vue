<template>
  <div>
    <mytable ref="table" :api="$api.shop.all">
      <div class="table-toolbar" slot="toolbar">
        <div class="toolbar-header">
          <div class="toolbar-title">{{$route.meta.title}}</div>
          <div class="toolbar-actions">
            <el-button type="primary" size="mini" @click="onAdd()">创建</el-button>
          </div>
        </div>
      </div>

      <el-table-column prop="id" label="ID" align="center" width="70"></el-table-column>
      <el-table-column prop="name" label="名称"></el-table-column>
      <el-table-column prop="_status" label="状态" align="center" width="70"></el-table-column>
      <el-table-column prop="hotline" label="服务热线" width="220" align="center"></el-table-column>
      <el-table-column prop="leader_name" label="负责人" width="140">
        <template slot-scope="scope">{{scope.row.leader_name + '[' + scope.row.leader_mobile + ']'}}</template>
      </el-table-column>
      <el-table-column prop="created" label="创建时间" width="220" align="center">
        <template slot-scope="scope">{{scope.row._created}}</template>
      </el-table-column>
      <el-table-column label="操作" width="90" class-name="table-action">
        <template slot-scope="scope">
          <el-button class="btn-action" size="mini" @click="onEdit(scope.row.id)" title="编辑">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
            </svg>
          </el-button>
          <el-button class="btn-action" size="mini" @click="onDel(scope.row)" title="删除">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#delete"></use>
            </svg>
          </el-button>
        </template>
      </el-table-column>
    </mytable>
  </div>
</template>

<script>
  export default {
    name: 'page-all-shop',
    data() {
      return {}
    },
    methods: {
      onAdd() {

      },
      onEdit(id) {

      },
      onDel(item) {

      }
    }
  }
</script>