<template>
  <div>
    <el-form :model="model" :rules="rules" ref="form" label-width="90px" size="small">
      <el-form-item label="客服电话" prop="hotline">
        <el-input v-model="model.hotline" maxlength="15" placeholder="座机号或手机号"></el-input>
      </el-form-item>
      <el-form-item label="联系地址" prop="address">
        <address-select></address-select>
      </el-form-item>
      <el-form-item label="详细地址" prop="address">
        <el-input v-model="model.address" maxlength="128" placeholder="请精确到街道门牌号"></el-input>
      </el-form-item>
      <el-form-item>
        <address-map></address-map>
      </el-form-item>
      <el-form-item class="form-action">
        <el-button type="primary">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import AddressSelect from '@/components/address';
  import AddressMap from '@/components/address/map';

  export default {
    components: {AddressSelect, AddressMap},
    data() {
      return {
        model: {
          hotline: '',
          address: ''
        },
        rules: {
          hotline: {required: true, message: '必填项', trigger: 'blur'},
          address: {required: true, message: '必填项', trigger: 'blur'}
        }
      }
    },
    methods: {
      save() {

      }
    }
  }
</script>