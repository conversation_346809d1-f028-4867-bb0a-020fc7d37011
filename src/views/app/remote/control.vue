<template>
  <div class="edit-panel-page">
    <div class="edit-panel-box">
      <div class="edit-panel-side">
        <div class="edit-panel-name">远程控制</div>

        <div class="edit-panel-menu">
          <div class="item" v-for="item in menu" :class="{ active: active == item.name }" @click="active = item.name">
            <div class="label">{{ item.label }}</div>
            <div class="value">
              {{ item.value }}<i class="el-icon-arrow-right"></i>
            </div>
          </div>
        </div>
      </div>
      <keep-alive>
        <component ref="part" v-bind:is="`${active}-panel`" class="edit-panel-content"></component>
      </keep-alive>
    </div>
  </div>
</template>

<script>
import UpdatePanel from './components/update-panel';
import CleanPanel from './components/clean-panel';
import UpgradePanel from './components/upgrade-panel';
import $require from '@/utils/require';

export default {
  name: 'AppRemoteControlPage',
  components: { UpdatePanel, CleanPanel, UpgradePanel },
  data() {
    return {
      active: 'update',
      menu: [
        { label: '更新数据', value: '', name: 'update' },
        { label: '清理缓存', value: '', name: 'clean' },
        { label: '版本升级', value: '', name: 'upgrade' },
      ],
    }
  },
  computed: {
    socket() {
      return window.io(this.$env.SOCKET_URL + '/admin', {
        autoConnect: false
      });
    },
  },
  mounted() {
    console.log(this.socket);
    let socket = this.socket;

    socket.on('connect', this.onConnected);
    socket.on('disconnect', this.onDisconnect);
    socket.on('app start', this.onAppStart);

    socket.open();
  },
  beforeRouteEnter(to, from, next) {
    $require('socket').then(next)
  },
  methods: {
    onConnected() {
      this.socket.emit('token', this.$env.token);
    },
    onDisconnect() {

    },
    onAppStart() {
      this.$notify({ title: 'APP', message: 'APP已启动', position: 'bottom-right' });
    }
  }
}
</script>