<template>
  <div>
    <mytable ref="table" :list="list" api="/v1/company.recruit.staff.list">
      <div class="table-toolbar" slot="toolbar">
        <div class="toolbar-header">
          <div class="toolbar-title">{{ $route.meta.title }}</div>
          <div class="toolbar-actions">
            <!-- <el-input class="search-val" size="mini" v-model="query.code" placeholder="请输入编号" style="width: 200px"
                      maxlength="5" clearable>
            </el-input>
            <el-button size="mini" icon="el-icon-search" title="搜索" circle @click="searchFun"></el-button> -->
            <el-button type="primary" size="mini" @click="onAdd">添加</el-button>
          </div>
        </div>
      </div>
      <el-table-column label="编号" prop="id" align="center"></el-table-column>
      <el-table-column label="姓名" prop="name" align="center"></el-table-column>
      <el-table-column label="所处阶段" prop="stage" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.stage == 1">入党申请人</div>
          <div v-if="scope.row.stage == 2">积极分子</div>
          <div v-if="scope.row.stage == 3">发展对象</div>
          <div v-if="scope.row.stage == 4">预备党员</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="90" class-name="table-action">
        <template slot-scope="scope">
          <el-button class="btn-action" size="mini" @click="onEdit(scope.row)" title="编辑">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
            </svg>
          </el-button>
          <el-button class="btn-action" size="mini" @click="onDelete(scope.row)" title="删除">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#delete"></use>
            </svg>
          </el-button>
        </template>
      </el-table-column>
    </mytable>
    <el-dialog :title="flag ? '添加发展党员' : '编辑发展党员'" :visible.sync="dialogVisible" width="800px" @closed="onClosed"
               :append-to-body="true" :close-on-click-modal="false">
      <el-form ref="form" label-width="80px" :model="model" :rules="rules">
        <el-form-item label="姓名" prop="name">
          <el-input v-model.trim="model.name" placeholder="必填项"></el-input>
        </el-form-item>
        <el-form-item label="所处阶段" prop="stage">
          <el-radio v-model="model.stage" :label="1">入党申请人</el-radio>
          <el-radio v-model="model.stage" :label="2">积极分子</el-radio>
          <el-radio v-model="model.stage" :label="3">发展对象</el-radio>
          <el-radio v-model="model.stage" :label="4">预备党员</el-radio>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSubmit">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import request from "@/api/modules/request.js"
import CoverImage from "@/views/media/image/cover";

export default {
  name: 'honour',
  components: { CoverImage },
  data() {
    return {
      list: [],
      flag: true,
      dialogVisible: false,
      model: {
        name: '',
        stage: ""
      },
      // query: {
      //   name: '',
      //   code: '',
      //   type: '1',
      //   offset: 0,
      //   limit: 15
      // }
    };
  },
  created() {

  },
  mounted() {

  },
  methods: {
    // searchFun() {
    //   request.get('/v1/region.info.query?name=' + this.query.name + '&&code=' + this.query.code + '&&type=1').then(res => {
    //     // console.log(res);
    //     this.list = res.rows
    //   })
    // },
    validate(callback) {
      this.$refs.form.validate(valid => {
        valid && callback();
      });
    },
    refresh() {
      this.model = {
        name: '',
        stage: ""
      }
      this.$refs.table.refresh();
    },
    onAdd() {
      this.flag = true
      this.model.stage = ''
      this.model.name = ''
      this.model.id = null
      this.dialogVisible = true
    },
    onSubmit() {
      this.validate(this.doSubmit);
    },
    doSubmit() {
      if (this.flag) {
        this.$api.post('/v1/company.recruit.staff.create', this.model).then(res => {
          // console.log(res);
          this.dialogVisible = false
          this.refresh()
        })
      } else {
        this.$api.post('/v1/company.recruit.staff.update', this.model).then(res => {
          this.dialogVisible = false
          this.refresh()
        })
      }
    },
    onEdit(row) {
      this.flag = false
      this.dialogVisible = true
      this.model = row
    },
    onDelete(row) {
      this.$confirm('此操作将永久删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.post('/v1/company.recruit.staff.delete', { id: row.id }).then(res => {
          this.refresh()
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    }
  },
};
</script>

<style lang="scss" scoped></style>