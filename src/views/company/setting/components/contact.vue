<template>
  <div class="edit-base">
    <div class="edit-panel-title">联系我们</div>
    <div class="edit-panel-desc">方便计算用户距离您的位置，以及获取您的联系方式</div>
    <el-form ref="form" :model="model" :rules="rules" class="edit-panel-body" label-width="90px" label-suffix=":" size="small">
      <el-form-item label="官方网站" prop="website">
        <el-input v-model.trim="model.website" placeholder="请输入" maxlength="128"></el-input>
      </el-form-item>
      <el-form-item label="客服电话">
        <el-input v-model.trim="model.service_phone" placeholder="请输入" maxlength="15"></el-input>
      </el-form-item>
      <el-form-item label="联系地址" prop="province_code">
        <el-address :province.sync="model.province_code" :city.sync="model.city_code" :district.sync="model.district_code"></el-address>
      </el-form-item>
      <el-form-item label="详细地址" prop="address">
        <el-input v-model.trim="model.address" id="companyAddress" placeholder="请输入" maxlength="128"></el-input>
      </el-form-item>
      <el-form-item label="地图坐标" prop="lng">
        <address-map input="companyAddress"
                     :province_code.sync="model.province_code"
                     :city_code.sync="model.city_code"
                     :district_code.sync="model.district_code"
                     :lng.sync="model.lng"
                     :lat.sync="model.lat"
                     :address.sync="model.address">
        </address-map>
      </el-form-item>
    </el-form>
    <div class="edit-panel-action">
      <el-button type="primary" @click="onSubmit">保 存</el-button>
    </div>
  </div>
</template>

<script>
import AddressMap from '@/components/address/map';
import ElAddress from "@/components/address/select";

export default {
  components: {ElAddress, AddressMap},
  data() {
    return {
      rules: {
        website: {
          trigger: 'blur', validator: (rule, value, callback) => {
            callback(validator.isURL(value) ? undefined : new Error('格式错误'));
          }
        },
        province_code: {
          required: true, trigger: 'blur', validator: (rule, value, callback) => {
            callback(!this.model.province_code || !this.model.city_code || !this.model.district_code ? new Error('请选择') : undefined);
          }
        },
        address: {required: true, trigger: 'blur', message: '必填项'},
        lng: {required: true, trigger: 'blur', message: '请选择坐标'}
      }
    }
  },
  computed: {
    model() {
      return this.$parent.model;
    }
  },
  methods: {
    onSubmit() {
      this.$refs.form.validate(valid => {
        valid && this.doSubmit();
      });
    },
    doSubmit() {
      let {model} = this;

      this.$api.post('/v1/company.info.update?action=contact', {
        id: model.id,
        website: model.website,
        service_phone: model.service_phone,
        province_code: model.province_code,
        city_code: model.city_code,
        district_code: model.district_code,
        address: model.address,
        lng: model.lng,
        lat: model.lat
      });
    }
  }
}
</script>