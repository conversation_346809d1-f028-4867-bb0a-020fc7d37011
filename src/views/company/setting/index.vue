<template>
  <div class="edit-panel-page company-setting-page" v-loading="loading">
    <div class="edit-panel-box">
      <div class="edit-panel-side">
        <div class="edit-panel-name">完善信息</div>
        <div class="edit-panel-menu">
          <div class="item" :class="{active:view=='base'}" @click="view='base'">
            <div class="label">基本信息</div>
            <div class="value">
              <i class="el-icon-arrow-right"></i>
            </div>
          </div>
          <div class="item" :class="{active:view=='intro'}" @click="view='intro'">
            <div class="label">文字介绍</div>
            <div class="value">
              {{ introValue }}<i class="el-icon-arrow-right"></i>
            </div>
          </div>
          <!-- 屏蔽党建不需要的选项 开始
          <div class="item" :class="{active:view=='work-reset'}" @click="view='work-reset'">
            <div class="label">作息时间</div>
            <div class="value">
              {{ timeValue }}<i class="el-icon-arrow-right"></i>
            </div>
          </div>
          <div class="item" :class="{active:view=='welfare'}" @click="view='welfare'">
            <div class="label">福利待遇</div>
            <div class="value">
              未完善<i class="el-icon-arrow-right"></i>
            </div>
          </div>
          <div class="item" :class="{active:view=='contact'}" @click="view='contact'">
            <div class="label">联系我们</div>
            <div class="value">
              {{ contactValue }}<i class="el-icon-arrow-right"></i>
            </div>
          </div>
          <div class="item" :class="{active:view=='image'}" @click="view='image'">
            <div class="label">相册图集</div>
            <div class="value">
              未完善<i class="el-icon-arrow-right"></i>
            </div>
          </div>
          <div class="item" :class="{active:view=='video'}" @click="view='video'">
            <div class="label">宣传视频</div>
            <div class="value">
              未完善<i class="el-icon-arrow-right"></i>
            </div>
          </div>
          <div class="item" :class="{active:view=='license'}" @click="view='license'">
            <div class="label">工商信息</div>
            <div class="value">
              未完善<i class="el-icon-arrow-right"></i>
            </div>
          </div>
          屏蔽党建不需要的选项 结束-->
        </div>
      </div>
      <div class="edit-panel-content">
        <component v-bind:is="`edit-${view}`"></component>
      </div>
    </div>
  </div>
</template>

<style lang="scss" src="../style.scss"></style>

<script>
import EditBase from './components/base';
import EditIntro from './components/intro';
import EditContact from './components/contact';
import EditImage from './components/images';
import EditVideo from './components/video';
import EditWelfare from './components/welfare';
import EditLicense from './components/license';
import EditWorkReset from './components/work-reset';

export default {
  components: {EditBase, EditIntro, EditContact, EditImage, EditVideo, EditWorkReset, EditWelfare, EditLicense},
  data() {
    return {
      loading: true,
      view: null,
      model: {}
    }
  },
  computed: {
    introValue() {
      return !!this.model.intro ? '已完善' : '未完善'
    },
    timeValue() {
      return !!this.model.rest_code ? '已完善' : '未完善'
    },
    contactValue() {
      return this.model.lng ? '已完善' : '未完善'
    }
  },
  created() {
    this.$api.get('/v1/company.info.get?id=' + this.$store.state.admin.company_id).then(data => {
      this.model = data;
      this.loading = false;
      this.view = 'base';
    });
  },
}
</script>