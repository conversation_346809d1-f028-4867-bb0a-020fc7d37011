<template>
  <div>
    <mytable ref="table" api="/v1/ioc.user.query" :query="query" :list="list" :check="query.user_tag" @fresh="clearFun">
      <div class="table-toolbar" slot="toolbar">
        <div class="toolbar-header">
          <div class="toolbar-title">{{ $route.meta.title }}</div>
          <div class="toolbar-actions">
            查询条件：<el-select v-model="value" placeholder="请选择" size="mini">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
            <el-input v-show="value == '1'" class="search-val" size="mini" v-model="query.name" :placeholder="placeholder1" style="width: 300px" maxlength="5" clearable> </el-input>
            <el-input v-show="value == '2'" class="search-val" size="mini" v-model="query.grid" :placeholder="placeholder2" style="width: 300px" maxlength="5" clearable> </el-input>
            <el-input v-show="value == '3'" class="search-val" size="mini" v-model="query.house" :placeholder="placeholder3" style="width: 300px" maxlength="5" clearable> </el-input>
            <el-input v-show="value == '4'" class="search-val" size="mini" v-model="query.gender" :placeholder="placeholder4" style="width: 300px" maxlength="5" clearable> </el-input>
            <el-checkbox-group v-model="query.user_tag" v-show="value == '5'">
              <el-checkbox label="1">特殊人群</el-checkbox>
              <el-checkbox label="2">小区名人</el-checkbox>
              <el-checkbox label="3">军人</el-checkbox>
              <el-checkbox label="4">重点监控</el-checkbox>
            </el-checkbox-group>
            <el-button size="mini" icon="el-icon-delete" title="清空所有查询条件" circle @click="clearFun"></el-button>
            <el-button size="mini" icon="el-icon-search" title="搜索" circle @click="searchFun"></el-button>
            <el-button type="primary" size="mini" @click="onAdd">添加</el-button>
          </div>
        </div>
      </div>
      <el-table-column label="姓名" prop="name" align="center" width="90px"></el-table-column>
      <el-table-column label="性别" prop="gender" align="center" width="60px"></el-table-column>
      <el-table-column label="年龄" prop="age" align="center" width="60px"></el-table-column>
      <el-table-column label="民族" prop="nation" align="center" width="60px"></el-table-column>
      <el-table-column label="身份证" prop="id_card" align="center" width="150px"></el-table-column>
      <el-table-column label="政治面貌" prop="political" align="center" width="80px"></el-table-column>
      <!-- <el-table-column label="住址" prop="address" align="center"></el-table-column> -->
      <el-table-column label="门牌号" prop="room" align="center"></el-table-column>
      <el-table-column label="户口登记类型" prop="domicile_type" align="center" width="100px"></el-table-column>
      <el-table-column label="户籍或外来" prop="domicile_from" align="center" width="100px"></el-table-column>
      <el-table-column label="小区名称" prop="house_name" align="center"></el-table-column>
      <el-table-column label="网格名称" prop="grid_name" align="center"></el-table-column>
      <el-table-column label="特殊人群" prop="special_group" align="center"></el-table-column>
      <el-table-column label="小区名人" prop="famous_type" align="center"></el-table-column>
      <el-table-column label="军人" prop="soldier_type" align="center"></el-table-column>
      <el-table-column label="重点监控" prop="focus_type" align="center"></el-table-column>
      <!-- <el-table-column label="备注" prop="remark" align="center"> -->
      <!-- </el-table-column> -->
      <el-table-column label="操作" width="90" class-name="table-action">
        <template slot-scope="scope">
          <el-button class="btn-action" size="mini" @click="onEdit(scope.row)" title="查看详情">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#edit"></use>
            </svg>
          </el-button>
          <el-button class="btn-action" size="mini" @click="onDelete(scope.row)" title="删除">
            <svg class="icon" aria-hidden="true">
              <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#delete"></use>
            </svg>
          </el-button>
        </template>
      </el-table-column>
    </mytable>

    <el-dialog :title="flag ? '添加居民' : '居民详情'" :visible.sync="dialogVisible" width="1200px" @closed="onClosed" :append-to-body="true" :close-on-click-modal="false">
      <el-form ref="form" label-width="120px" :model="model" :inline="true">
        <el-form-item label="姓名" prop="name">
          <el-input v-model.trim="model.name" placeholder="必填项" :readonly="!flag"></el-input>
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="model.gender">
            <el-radio label="男"></el-radio>
            <el-radio label="女"></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="家庭关系" prop="family_relation" style="margin-left: 95px">
          <el-input v-model.trim="model.family_relation" placeholder="必填项" :readonly="!flag"></el-input>
        </el-form-item>
        <el-form-item label="民族" prop="nation">
          <el-input v-model.trim="model.nation" placeholder="必填项" :readonly="!flag"></el-input>
        </el-form-item>
        <el-form-item label="身份证" prop="id_card">
          <el-input v-model.trim="model.id_card" placeholder="必填项" :readonly="!flag"></el-input>
        </el-form-item>
        <el-form-item label="现住址" prop="address">
          <el-input v-model.trim="model.address" placeholder="必填项" :readonly="!flag"></el-input>
        </el-form-item>
        <el-form-item label="门牌号" prop="room">
          <el-input v-model.trim="model.room" placeholder="必填项" :readonly="!flag"></el-input>
        </el-form-item>
        <el-form-item label="户口登记类型" prop="domicile_type">
          <el-input v-model.trim="model.domicile_type" placeholder="必填项" :readonly="!flag"></el-input>
        </el-form-item>
        <el-form-item label="户籍或外来" prop="domicile_from">
          <el-input v-model.trim="model.domicile_from" placeholder="必填项" :readonly="!flag"></el-input>
        </el-form-item>
        <el-form-item label="政治面貌" prop="political">
          <el-input v-model.trim="model.political" placeholder=""></el-input>
        </el-form-item>
        <!-- <el-form-item label="网格编码" prop="grid_code">
          <el-input v-model.trim="model.grid_code" placeholder="必填项"></el-input>
        </el-form-item> -->
        <el-form-item label="网格名称" prop="grid_name">
          <el-input v-model.trim="model.grid_name" placeholder="必填项" :readonly="!flag"></el-input>
        </el-form-item>
        <!-- <el-form-item label="小区编码" prop="house_code">
          <el-input v-model.trim="model.house_code" placeholder="必填项"></el-input>
        </el-form-item> -->
        <el-form-item label="小区名称" prop="house_name">
          <el-input v-model.trim="model.house_name" placeholder="必填项" :readonly="!flag"></el-input>
        </el-form-item>
        <el-form-item label="职业" prop="occupation">
          <el-input v-model.trim="model.occupation" placeholder="必填项" :readonly="!flag"></el-input>
        </el-form-item>
        <el-form-item label="电话" prop="phone">
          <el-input v-model.trim="model.phone" placeholder="必填项" :readonly="!flag"></el-input>
        </el-form-item>
        <el-form-item label="入伍日期" prop="soldier_start_time">
          <el-input v-model.trim="model.soldier_start_time" placeholder="必填项" :readonly="!flag"></el-input>
        </el-form-item>
        <el-form-item label="退伍日期" prop="soldier_end_time">
          <el-input v-model.trim="model.soldier_end_time" placeholder="必填项" :readonly="!flag"></el-input>
        </el-form-item>
        <el-form-item label="荣誉" prop="soldier_honor_grade">
          <el-input v-model.trim="model.soldier_honor_grade" placeholder="必填项" :readonly="!flag"></el-input>
        </el-form-item>
        <el-form-item label="特殊人群:" prop="special_group">
          <el-checkbox-group v-model="model.special_group">
            <el-checkbox label="低保户"></el-checkbox>
            <el-checkbox label="失独家庭"></el-checkbox>
            <el-checkbox label="困境儿童"></el-checkbox>
            <el-checkbox label="孤寡老人"></el-checkbox>
            <el-checkbox label="精神病患者"></el-checkbox>
            <el-checkbox label="一级残疾人"></el-checkbox>
            <el-checkbox label="二级残疾人"></el-checkbox>
            <el-checkbox label="三级残疾人"></el-checkbox>
            <el-checkbox label="四级残疾人"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <br />
        <el-form-item label="小区名人:" prop="famous_type">
          <el-checkbox-group v-model="model.famous_type">
            <el-checkbox label="致富带头人"></el-checkbox>
            <el-checkbox label="文化名流"></el-checkbox>
            <el-checkbox label="职业能人"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="军人:" prop="soldier_type">
          <el-checkbox-group v-model="model.soldier_type">
            <el-checkbox label="退役军人"></el-checkbox>
            <el-checkbox label="现役军人"></el-checkbox>
            <el-checkbox label="预备役"></el-checkbox>
            <el-checkbox label="优抚对象"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <br />
        <el-form-item label="重点监控:" prop="focus_type">
          <el-checkbox-group v-model="model.focus_type">
            <el-checkbox label="社区矫正"></el-checkbox>
            <el-checkbox label="刑满释放"></el-checkbox>
            <el-checkbox label="吸毒人员"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <br />
        <el-form-item label="备注" prop="remark">
          <el-input v-model.trim="model.remark" placeholder="备注" style="width: 800px"></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSubmit">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/api/modules/request.js'
export default {
  name: 'grid',

  data() {
    return {
      list: [],
      find: '',
      query: {
        name: '',
        grid: '',
        house: '',
        gender: '',
        user_tag: [],
      },
      dialogVisible: false,
      flag: true,
      model: {
        name: '',
        family_relation: '',
        gender: '',
        nation: '',
        id_card: '',
        address: '',
        room: '',
        domicile_type: '',
        domicile_from: '',
        political: '',
        grid_code: '',
        grid_name: '',
        house_code: '',
        house_name: '',
        special_group: [],
        famous_type: [],
        soldier_type: [],
        focus_type: [],
        remark: '',
      },
      options: [
        {
          value: '1',
          label: '姓名',
        },
        {
          value: '2',
          label: '网格编码',
        },
        {
          value: '3',
          label: '小区编码',
        },
        {
          value: '4',
          label: '性别',
        },
        {
          value: '5',
          label: '其他',
        },
      ],
      value: '1',
      placeholder1: '请输入姓名',
      placeholder2: '请输入网格编码',
      placeholder3: '请输入小区编码',
      placeholder4: '请输入性别',
    }
  },
  mounted() {},
  methods: {
    clearFun() {
      this.query.name = ''
      this.query.grid = ''
      this.query.house = ''
      this.query.gender = ''
      this.query.user_tag = []
    },
    refresh() {
      this.$refs.table.refresh()
    },
    searchFun() {
      this.$refs.table.refresh({ name: this.query.name, grid: this.query.grid, gender: this.query.gender, house: this.query.house, user_tag: this.query.user_tag.toString() })
    },
    onAdd() {
      this.flag = true
      this.model.id = 0
      this.model.name = ''
      this.model.family_relation = ''
      this.model.gender = ''
      this.model.nation = ''
      this.model.id_card = ''
      this.model.address = ''
      this.model.room = ''
      this.model.domicile_type = ''
      this.model.domicile_from = ''
      this.model.political = ''
      this.model.grid_code = ''
      this.model.grid_name = ''
      this.model.house_code = ''
      this.model.house_name = ''
      this.model.special_group = []
      this.model.famous_type = []
      this.model.soldier_type = []
      this.model.focus_type = []
      this.model.remark = ''
      this.dialogVisible = true
    },
    onEdit(row) {
      console.log(row)
      this.flag = false
      request.get('/v1/ioc.user.get?id=' + row.id).then((res) => {
        // console.log(res);
        this.model = res
        this.dialogVisible = true
      })
    },
    onDelete(row) {
      this.$confirm('此操作将永久删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          request.post('/v1/ioc.user.delete', { id: row.id }).then((res) => {
            this.refresh()
            this.$message({
              type: 'success',
              message: '删除成功!',
            })
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
          })
        })
    },
    onSubmit() {
      if (this.flag) {
        request.post('/v1/ioc.user.create', this.model).then((res) => {
          // console.log(res);
          this.dialogVisible = false
          this.refresh()
        })
      } else {
        //
        request.post('/v1/ioc.user.update', this.model).then((res) => {
          // console.log(res);
          this.dialogVisible = false
          this.refresh()
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped></style>
