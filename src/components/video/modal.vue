<template>
  <el-dialog class="video-modal" :title="`播放地址·${model.text}`"
             :visible.sync="visible" @closed="closed" :append-to-body="true" width="600px">
    <el-form ref="form" label-width="100px" auto-complete="off" :model="model" :rules="rules" style="margin-right:40px;" size="small">
      <el-form-item label="分辨率" prop="resolution">
        <el-select v-model="model.resolution" filterable allow-create placeholder="请选择或输入" style="display:block">
          <el-option label="960 x 540" value="960x540"></el-option>
          <el-option label="1280 x 720" value="1280x720"></el-option>
          <el-option label="1920 x 1080" value="1920x1080"></el-option>
          <el-option label="3840 x 2160" value="3840x2160"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="别名" prop="text">
        <el-select v-model="model.text" style="display:block">
          <el-option value="流畅"></el-option>
          <el-option value="标清"></el-option>
          <el-option value="高清"></el-option>
          <el-option value="超清"></el-option>
          <el-option value="4K"></el-option>
          <el-option value="原画"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="mp4" prop="mp4">
        <el-input v-model="model.mp4"></el-input>
      </el-form-item>
      <el-form-item label="m3u8" prop="m3u8">
        <el-input v-model="model.m3u8"></el-input>
      </el-form-item>
      <el-form-item label="flv" prop="flv">
        <el-input v-model="model.flv"></el-input>
      </el-form-item>
      <el-form-item label="rtmp" prop="rtmp">
        <el-input v-model="model.rtmp"></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="remove" size="small">移 除</el-button>
      <el-button type="primary" size="small" @click="submit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  export default {
    data() {
      return {
        model: {},
        rules: {
          resolution: {required: true, trigger: 'blur', message: '请选择或输入'},
          mp4: {trigger: 'blur', validator: this.validUrl},
          flv: {trigger: 'blur', validator: this.validUrl},
          m3u8: {trigger: 'blur', validator: this.validUrl},
          rtmp: {trigger: 'blur', validator: this.validRtmp}
        }
      }
    },
    methods: {
      closed() {
        this.$refs.form.clearValidate();
        this.$emit('update:visible', false);
      },
      hasUrl() {
        let {model} = this;
        return model.mp4 || model.flv || model.m3u8 || model.rtmp;
      },
      validUrl(rule, value, callback) {
        if (!this.hasUrl()) {
          callback('请至少输入一个可播放地址');
        } else {
          callback(validator.isURL(value, {protocols: ['http', 'https']}) ? null : '格式错误');
        }
      },
      validRtmp(rule, value, callback) {
        if (!this.hasUrl()) {
          callback('请至少输入一个可播放地址');
        } else {
          callback(validator.isURL(value, {protocols: ['rtmp']}) ? null : '格式错误');
        }
      },
      submit() {
        this.$refs.form.validate(valid => {
          if (!valid) return;

          let {model} = this;
          model.checked = true;

          Object.assign(this.value, model);
          this.closed();
        });
      },
      remove() {
        this.value.checked = false;
        this.closed();
      }
    }
  }
</script>