<template>
  <div class="iphone-x">
    <div class="iPhoneX-StatusBar">
      <div class="StatusBarTime">10:00</div>
      <div class="StatusBarSignal">
        <svg class="icon" aria-hidden="true">
          <use xlink:href="#signal"></use>
        </svg>
      </div>
      <div class="StatusBarWifi">
        <svg class="icon" aria-hidden="true">
          <use xlink:href="#wifi"></use>
        </svg>
      </div>
      <div class="StatusBarBattery">
        <svg class="icon" aria-hidden="true">
          <use xlink:href="#battery"></use>
        </svg>
      </div>
    </div>

    <div class="iphone-header top-sticky">
      <div class="iphone-header-content">
        <div class="iphone-header-left">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#back-ios"></use>
          </svg>
          <span>返回</span>
        </div>
        <div class="iphone-header-title">{{title}}</div>
        <div class="iphone-header-right">
          <svg class="icon" aria-hidden="true">
            <use xlink:href="#gengduo"></use>
          </svg>
        </div>
      </div>
    </div>

    <div class="iphone-body">
      <slot></slot>
    </div>

    <div class="iphone-footer-bar"></div>
  </div>
</template>

<script>
  export default {
  	name: 'mobile',
    props: ['title']
  }
</script>

<style lang="scss">
  .iphone-x{position:relative;width:432px;height:861px;background: url(cdn('/sys/iphone-x.png')) 100% no-repeat;overflow:hidden;padding:22px 30px 22px 27px;box-sizing:border-box}
  .iPhoneX-StatusBar{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;height:44px;padding:0 20px}
  .iPhoneX-StatusBar .StatusBarTime{-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;margin-right:auto;margin-left:7px;width:54px;font-size:14px;font-weight:600}
  .iPhoneX-StatusBar .StatusBarSignal{margin-left:auto}
  .iphone-x .StatusBarTime,.iphone-x .StatusBarSignal,.iphone-x .StatusBarWifi,.iphone-x .StatusBarBattery,.iphone_x_status_bar .StatusBarTime,.iphone_x_status_bar .StatusBarSignal,.iphone_x_status_bar .StatusBarWifi,.iphone_x_status_bar .StatusBarBattery,.android_status_bar .StatusBarTime,.android_status_bar .StatusBarSignal,.android_status_bar .StatusBarWifi,.android_status_bar .StatusBarBattery{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;height:100%}
  .iPhoneX-StatusBar svg{font-size:20px}
  .iphone-header-content{display:flex;justify-content:center;align-items:center;position:relative;text-align:center;white-space:nowrap;min-height:45px;font-size:14px;padding:0 15px}
  .iphone-header-left{position:relative;flex:.3;text-align:left}
  .iphone-header-title{position:relative;flex:1;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-size:inherit;font-weight:400}
  .iphone-header-right{position:relative;flex:.3;text-align:right;font-size:15px}
  .iphone-header .arrow-bottom:after{border-color:#fff}
  .iphone-body{height:726px;width:100%;overflow-y:auto;overflow-x:hidden;margin-left:1px;border-bottom-left-radius:30px;border-bottom-right-radius: 30px;}
  .iphone-body::-webkit-scrollbar{display:none}
  .iphone-footer-bar{position: absolute; bottom: 44px; left: 30%; right: 30%;height: 3px; background-color: black; border-radius: 6px;}
</style>
