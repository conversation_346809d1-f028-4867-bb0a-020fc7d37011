.sidebar-container {
  user-select: none;
  
  &::-webkit-scrollbar {
    display: none
  }
  
  svg.icon {
    margin-right: 6px;
  }
}

.sidebar-simple {
  .menu-item {
    cursor: pointer;
    line-height: 50px;
    overflow: hidden;
    
    &.opened > .menu-group:after {
      transform: rotate(90deg);
    }
    
    .menu-list {
      display: none;
    }
  }
  
  .menu-group {
    position: relative;
    padding: 0 20px;
    
    &:after {
      font-family: element-icons !important;
      content: "\e6e0";
      position: absolute;
      right: 20px;
      transition: transform .3s;
      transform: rotate(0);
    }
  }
  
  .menu-title {
    padding: 0 20px;
    
    &:hover, &.active {
      color: #409EFF;
    }
  }
  
  &.only {
    .group-title {
      display: none;
    }
    
    .menu-group:after {
      display: none;
    }
    
    .menu-item .menu-list {
      display: none !important;
    }
  }
}