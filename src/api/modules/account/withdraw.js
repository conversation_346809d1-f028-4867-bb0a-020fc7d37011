import request from '../request';
import utils from '@/utils';

export default {
  status: {
    0 : '待处理',
    1 : '处理中',
    2 : '已处理',
    3 : '已拒绝'
  },
  format(item){
    item.created_format = utils.Date(item.created).toString();

    switch (item.audit_result) {
      case 0:
        item._audit_result = '待处理';
        break;
      case 1:
        item._audit_result = '处理中';
        break;
      case 2:
        item._audit_result = '已处理';
        break;
      case 3:
        item._audit_result = '已拒绝';
        break;
      default:
        item._audit_result = '非法状态';
        break;
    }

    return item;
  },
  list(params){
    return request.get('account.withdraw.query', params).then(res => {
      res.rows.forEach(this.format);
      return res;
    });
  },
  audit(data){
    return request.post('account.withdraw.audit', data);
  }
}