import request from './request';
import utils from '@/utils';

function  format(m){
  m.trade_created_str=utils.Date(m.trade_created).toString("YYYY-MM-DD HH:mm:ss");
  switch (m.shipping_type) {
    case 'express' :
      m.shipping_type_str='快递';
      break;
    case 'selffetch' :
      m.shipping_type_str='上门自提';
      break;
    case 'virtual' :
      m.shipping_type_str='无需物流';
      break;
  }
}
export default {
  list(query){
    return request.get('trade.query', query).then(res=>{
      if(res.total == 0){
        return res;
      }
      res.rows.forEach(format);
      return res;
    });
  },
  remark(params){
    return request.post('trade.remark',params);
  },
  close(params){
    return request.post('trade.close',params)
  },
  sendGoods(tid, params){
    return request.post('trade.sendGoods', {tid: tid, params: params})
  },
  trade_options:[
    {label:'全部',value:'all'},
    {label:'待付款',value:'wait_buyer_pay'},
    {label:'待发货',value:'wait_seller_send'},
    {label:'待收货',value:'wait_buyer_receive'},
    {label:'交易成功',value:'success'},
    {label:'已取消',value:'cancel'},
    {label:'已关闭',value:'closed'},]
}