import Vue from 'vue';
import router from "./router";
import $store from "./store";
import $env from "./utils/env";
import $utils from "./utils";
import $request from "./utils/request";
import $require from "./utils/require";
import $api from "./api";
import "./directive";
import "./components";
import App from "./App.vue";
// import { formatDate } from './views/app/home/<USER>';
// import scroll from 'vue-seamless-scroll'
// Vue.use(scroll)
Vue.mixin({
  computed: {
    CDN_URL() {
      return process.env.VUE_APP_CDN;
    },
    APP_URL() {
      return process.env.VUE_APP_URL;
    },
    NO_COVER() {
      return process.env.VUE_APP_CDN + "/sys/nocover.jpg";
    },
  },
  beforeCreate() {
    this.$api = $api;
    this.$env = $env;
    this.$require = $require;
    this.$utils = $utils;
    this.$store = $store;
  },
});
//时间过滤器
Vue.filter("filterTime", (dateTimeStamp) => {
  dateTimeStamp = new Date(dateTimeStamp);
  var minute = 1000 * 60;
  var hour = minute * 60;
  var day = hour * 24;
  var halfamonth = day * 15;
  var month = day * 30;
  var now = new Date().getTime();
  var diffValue = now - dateTimeStamp;
  if (diffValue < 0) {
    return;
  }
  var monthC = diffValue / month;
  var weekC = diffValue / (7 * day);
  var dayC = diffValue / day;
  var hourC = diffValue / hour;
  var minC = diffValue / minute;
  var result = "";
  if (monthC >= 1) {
    result = "" + parseInt(monthC) + "月前";
  } else if (weekC >= 1) {
    result = "" + parseInt(weekC) + "周前";
  } else if (dayC >= 1) {
    result = "" + parseInt(dayC) + "天前";
  } else if (hourC >= 1) {
    result = "" + parseInt(hourC) + "小时前";
  } else if (minC >= 1) {
    result = "" + parseInt(minC) + "分钟前";
  } else result = "刚刚";
  return result;
});
//图片加载失败展示默认图片
Vue.directive("errorimg", {
  //执行时机：绑定了当前指令的元素的所有属性和事件
  inserted(el) {
    //获取的是真是的dom元素，可以使用dom的所有事件
    console.log(el);
    el.onerror = function () {
      //将默认图片赋值给el.src属性
      el.src = "../errorImg.png";
    };
  },
});

new Vue({
  router,
  render: (h) => h(App),
  computed: {
    title: {
      get() {
        return document.title;
      },
      set(v) {
        document.title = v;
      },
    },
  },
  created() {
    $request.on("error", this.onRequestError);
  },
  methods: {
    onRequestError(e) {
      if (e.code == 401) {
        if (this.$store.state.admin.status == 0) {
          return this.$router.replace({
            name: "login",
            query: { redirect: encodeURIComponent(location.href) },
          });
        }

        this.$confirm("当前处于离线状态，请登录后继续", "提示", {
          confirmButtonText: "在新窗口登录",
          cancelButtonText: "取消",
          type: "warning",
          closeOnClickModal: false,
          distinguishCancelAndClose: true,
          beforeClose(action, instance, done) {
            if (action != "confirm") return done();

            window.open(
              "/login?target=parent&redirect=" +
                encodeURIComponent(location.href)
            );
            window.LoginSuccess = function (res) {
              $store.commit("SET_LOGIN", res);
              done();
            };
          },
        });
      } else {
        this.$message.error(e.toString());
      }
    },
  },
}).$mount("#app");
