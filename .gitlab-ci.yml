stages:
  - build
  - deploy
 
cache:
  key: ${CI_BUILD_REF_NAME}
  paths:
    - node_modules/
 
#before_script:
#  - export PATH=/usr/local/bin:$PATH
#  - 'which ssh-agent || ( yum update -y && yum install openssh-clients -y )'
#  - eval $(ssh-agent -s)
# - ssh-add <(echo "$SSH_PRIVATE_KEY")
# - mkdir -p ~/.ssh
# - chmod 700 ~/.ssh
# - echo "$SSH_KNOWN_HOSTS" > ~/.ssh/known_hosts
# - chmod 644 ~/.ssh/known_hosts
# - '[[ -f /.dockerenv ]] && echo -e "Host *\\n\\tStrictHostKeyChecking no\\n\\n" > ~/.ssh/config'
 
production-build:
  stage: build
  tags:
    - node-web-deploy   # 这个是runner的tags内容
  script:
    - npm install
    - npm run build     # console项目,web打包
    - echo 'Finished'
  artifacts:
    paths:
      - dist/

production-deploy:
  stage: deploy
  tags:
    - node-web-deploy   # 这个是runner的tags内容
  only:
    - master
  script:
    - sudo cp -r dist/* /www/dangjian/console  # 将打包好的dist拷贝到部署目录
    - echo 'Finished'
